using Microsoft.AspNetCore.Mvc;
using TheDash.Models;
using TheDash.Services;

namespace TheDash.Controllers
{
    /// <summary>
    /// Controller for virtual machine emulator functionality
    /// Provides CRUD operations for development and testing scenarios
    /// </summary>
    public class EmulatorController : Controller
    {
        private readonly IEmulatorService _emulatorService;

        public EmulatorController(IEmulatorService emulatorService)
        {
            _emulatorService = emulatorService ?? throw new ArgumentNullException(nameof(emulatorService));
        }

        // GET: /emulator
        public async Task<IActionResult> Index()
        {
            try
            {
                // Return the emulator page - not accessible via navigation menu
                var machines = await _emulatorService.GetVirtualMachinesAsync();
                var viewModel = new EmulatorViewModel
                {
                    Machines = machines
                };
                return View(viewModel);
            }
            catch (Exception ex)
            {
                // Log error and return error view
                var viewModel = new EmulatorViewModel();
                ViewBag.ErrorMessage = $"Error loading emulator: {ex.Message}";
                return View(viewModel);
            }
        }

        // GET: /api/emulator/machines - Retrieve virtual machines
        [HttpGet]
        [Route("api/emulator/machines")]
        public async Task<IActionResult> GetMachines()
        {
            try
            {
                var machines = await _emulatorService.GetVirtualMachinesAsync();
                return Json(new ApiResponse<List<VirtualMachine>>
                {
                    Success = true,
                    Message = "Machines retrieved successfully",
                    Data = machines
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error retrieving machines: {ex.Message}"
                });
            }
        }

        // POST: /api/emulator/machines - Create new virtual machine
        [HttpPost]
        [Route("api/emulator/machines")]
        public async Task<IActionResult> CreateMachine([FromBody] CreateMachineRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new ApiResponse<object>
                    {
                        Success = false,
                        Message = $"Validation errors: {string.Join(", ", errors)}"
                    });
                }

                var virtualMachine = await _emulatorService.CreateMachineAsync(request);

                return Json(new ApiResponse<VirtualMachine>
                {
                    Success = true,
                    Message = "Machine created successfully",
                    Data = virtualMachine
                });
            }
            catch (InvalidOperationException ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error creating machine: {ex.Message}"
                });
            }
        }

        // PUT: /api/emulator/machines/{id} - Update machine data
        [HttpPut]
        [Route("api/emulator/machines/{id}")]
        public async Task<IActionResult> UpdateMachine(int id, [FromBody] UpdateMachineRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new ApiResponse<object>
                    {
                        Success = false,
                        Message = $"Validation errors: {string.Join(", ", errors)}"
                    });
                }

                var virtualMachine = await _emulatorService.UpdateMachineAsync(id, request);

                return Json(new ApiResponse<VirtualMachine>
                {
                    Success = true,
                    Message = "Machine updated successfully",
                    Data = virtualMachine
                });
            }
            catch (InvalidOperationException ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error updating machine: {ex.Message}"
                });
            }
        }

        // DELETE: /api/emulator/machines/{id} - Delete virtual machine
        [HttpDelete]
        [Route("api/emulator/machines/{id}")]
        public async Task<IActionResult> DeleteMachine(int id)
        {
            try
            {
                var deleted = await _emulatorService.DeleteMachineAsync(id);

                if (!deleted)
                {
                    return Json(new ApiResponse<object>
                    {
                        Success = false,
                        Message = "Machine not found"
                    });
                }

                return Json(new ApiResponse<object>
                {
                    Success = true,
                    Message = "Machine deleted successfully"
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error deleting machine: {ex.Message}"
                });
            }
        }

        // GET: /api/emulator/dashboard-data - Get data for dashboard integration testing
        [HttpGet]
        [Route("api/emulator/dashboard-data")]
        public async Task<IActionResult> GetDashboardData()
        {
            try
            {
                var machines = await _emulatorService.GetVirtualMachinesAsync();
                var dashboardData = machines.Select(m => new
                {
                    m.MachineId,
                    m.MachineName,
                    m.Status,
                    m.Throughput,
                    m.YieldRate,
                    m.CycleTime,
                    m.LastUpdated,
                    IsOnline = m.Status == "Running"
                }).ToList();

                return Json(new ApiResponse<object>
                {
                    Success = true,
                    Message = "Dashboard data retrieved successfully",
                    Data = dashboardData
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error retrieving dashboard data: {ex.Message}"
                });
            }
        }

        // POST: /api/emulator/machines/{id}/data - Add machine data record
        [HttpPost]
        [Route("api/emulator/machines/{id}/data")]
        public async Task<IActionResult> AddMachineData(int id, [FromBody] MachineDataUpdate dataUpdate)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
                    return Json(new ApiResponse<object>
                    {
                        Success = false,
                        Message = $"Validation errors: {string.Join(", ", errors)}"
                    });
                }

                var virtualMachine = await _emulatorService.AddMachineDataAsync(id, dataUpdate);

                return Json(new ApiResponse<VirtualMachine>
                {
                    Success = true,
                    Message = "Machine data updated successfully",
                    Data = virtualMachine
                });
            }
            catch (InvalidOperationException ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = ex.Message
                });
            }
            catch (Exception ex)
            {
                return Json(new ApiResponse<object>
                {
                    Success = false,
                    Message = $"Error updating machine data: {ex.Message}"
                });
            }
        }
    }
}
