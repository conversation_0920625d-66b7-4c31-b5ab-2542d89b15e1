/* _content/TheDash/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-sb06g6maoz] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-sb06g6maoz] {
  color: #0077cc;
}

.btn-primary[b-sb06g6maoz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-sb06g6maoz], .nav-pills .show > .nav-link[b-sb06g6maoz] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-sb06g6maoz] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-sb06g6maoz] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-sb06g6maoz] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-sb06g6maoz] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-sb06g6maoz] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
