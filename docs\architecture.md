# Comprehensive Production Monitoring and Analytics Dashboard Architecture Design Document

**Version:** 2.0
**Created On:** August 5, 2025
**Architect:** Winston

---

## 1. Architecture Overview

This project uses a Monolith architecture, where a single ASP.NET Core MVC application handles core logic. Data is stored in SQL Server, and the frontend communicates with the backend via an API. To implement predictive maintenance, a new, separate Python service has been added to run the AI/ML model.

## 2. Technology Stack Validation

| Category | Technology | Version | Purpose |
|---|---|---|---|
| Backend | ASP.NET Core MVC | 8.0+ | High-performance backend service, handling API requests and business logic. |
| Database | SQL Server TSQL | 2019+ | Enterprise-grade relational database for storing structured data. |
| Frontend | HTML/CSS/JS | Latest | Standard frontend stack for implementing the user interface and interactions. |
| **[Added]** AI/ML | Python + Scikit-learn | 3.10+ | To implement the predictive maintenance model. |
| Deployment | Local Execution | N/A | Simple local deployment, without dependencies on Docker or cloud services. |

## 3. Database Architecture Design

### 3.1 Machines Table (Machine Information)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| MachineId | INT | PRIMARY KEY, IDENTITY(1,1) | Unique machine ID. |
| MachineName | NVARCHAR(100) | NOT NULL, UNIQUE | Unique name of the machine. |
| MachineType | NVARCHAR(50) | NOT NULL | Type of the machine. |
| ProductionLine | NVARCHAR(50) | NOT NULL | Production line the machine belongs to. |
| IsVirtual | BIT | NOT NULL, DEFAULT(1) | Flag indicating if the machine is virtual. |

### 3.2 MachineData Table (Real-time Machine Data)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| DataId | BIGINT | PRIMARY KEY CLUSTERED | Unique data record ID, used as the clustered index. |
| MachineId | INT | FOREIGN KEY REFERENCES Machines(MachineId) | Foreign key to the Machines table. |
| Timestamp | DATETIME2 | NOT NULL | The timestamp of data collection. |
| Status | NVARCHAR(20) | NOT NULL | The machine's status. |
| Uptime | DECIMAL(18, 2) | | Uptime. |
| Downtime | DECIMAL(18, 2) | | Downtime. |
| CycleTime | DECIMAL(18, 2) | | Cycle time. |
| Throughput | INT | | Throughput. |
| YieldRate | DECIMAL(5, 2) | | Yield rate. |
| DefectRate | DECIMAL(5, 2) | | Defect rate. |
| DefectType | NVARCHAR(200) | | Type of defects. |

**Indexing Strategy:**
- Create a non-clustered index on `(MachineId, Timestamp)` to optimize queries for a specific machine's data within a time range.

### 3.3 Alerts Table (Alert Records)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| AlertId | INT | PRIMARY KEY IDENTITY(1,1) | Unique alert ID. |
| MachineId | INT | FOREIGN KEY REFERENCES Machines(MachineId) | Foreign key to the Machines table. |
| Timestamp | DATETIME2 | NOT NULL | The timestamp when the alert was triggered. |
| AlertType | NVARCHAR(50) | NOT NULL | The type of alert. |
| Reason | NVARCHAR(MAX) | NOT NULL | The detailed reason for the alert. |
| Status | NVARCHAR(20) | NOT NULL, DEFAULT('Pending') | The status of the alert. |
| ResolvedTimestamp | DATETIME2 | NULL | The timestamp when the alert was resolved. |
| ResolvedBy | NVARCHAR(100) | NULL | The user who resolved the alert. |
| **[Added]** PredictionRisk | FLOAT | NULL | AI model's predicted failure risk score. |

**Indexing Strategy:**
- Create a non-clustered index on `(MachineId, Timestamp)` to quickly query alert records for a specific machine within a time range.

### 3.4 Localizations Table (Localization Text)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| LocalizationId | INT | PRIMARY KEY IDENTITY(1,1) | Unique localization ID. |
| Key | NVARCHAR(200) | NOT NULL | The unique key for the text string. |
| Language | NVARCHAR(10) | NOT NULL | The language code. |
| Value | NVARCHAR(MAX) | NOT NULL | The translated text for the corresponding language. |

**Indexing Strategy:**
- Create a unique non-clustered index on `(Key, Language)` to ensure fast and unique lookups of translated text.

## 4. API Specification

**API Version:** 2.0
**Base URL:** `https://[your-api-domain]/api`
**Data Format:** JSON

**API 2.1: Get Production Line Overview Data**
- **HTTP Method:** `GET`
- **Endpoint:** `/dashboard/overview`
- **Description:** Retrieves overall KPI overview data for all production lines.
- **Request Parameters:** `startDate`, `endDate`, `productionLine` (optional)

**API 3.1: Get Latest Alerts List**
- **HTTP Method:** `GET`
- **Endpoint:** `/alerts/latest`
- **Description:** Retrieves the latest alerts from the system.

**API 2.0.1 [Added]: Trigger AI Model Prediction**
- **HTTP Method:** `POST`
- **Endpoint:** `/predictive/predict`
- **Description:** Sends the latest machine data to the AI/ML service and retrieves the failure prediction result.
- **Request Example:**
    ```json
    {
      "machineId": 1,
      "data": [...] // Machine metric data
    }
    ```
- **Response Example:**
    ```json
    {
      "machineId": 1,
      "predictionScore": 0.85, // Failure risk score
      "message": "High risk of failure detected in next 24 hours."
    }
    ```
---

## 5. Deployment Plan

This deployment plan is for local execution.

1.  **Prerequisites:** Install .NET SDK (8.0+), SQL Server, Git, Visual Studio, Python (3.10+), and Scikit-learn.
2.  **Get Code:** Use `git clone` to get the project code.
3.  **Set Up Database:** Create the database and tables, and update the connection string in `appsettings.json`.
4.  **Run Application:** Press F5 in Visual Studio or use `dotnet run` to start the backend application.
5.  **Run AI/ML Service:** Run the independent Python service, which listens for requests from the ASP.NET Core application.
6.  **Access Application:** Access the application via a web browser at `https://localhost:7001/`.

---

### Mermaid Diagrams Plaintext

Below is the plaintext code for the updated system component and deployment diagrams, which now include the new AI/ML service component.

```mermaid
graph TD
    A[Browser/Frontend] -->|HTTP/S Request| B(ASP.NET Core Application)
    B -->|SQL Query| C(SQL Server Database)
    B -->|Data Processing| D(Background Service)
    D -->|Data Generation/Alert Trigger| C
    B --Real-time Data--> E(AI/ML Prediction Service)
    E --Prediction Result--> C
    C -->|Data Return| B
    B -->|HTML/JSON Response| A
```

```mermaid
graph TD
    subgraph Local Machine
        A[Browser] --> B(ASP.NET Core Application)
        C[Background Service] --> B
        D[SQL Server] --> B
        E[AI/ML Prediction Service] --> B
    end
    B --> D
    B -- Local Network --> A
```