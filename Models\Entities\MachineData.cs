using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TheDash.Models.Entities
{
    [Table("MachineData")]
    public class MachineData
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public long DataId { get; set; }

        [Required]
        [ForeignKey("Machine")]
        public int MachineId { get; set; }

        [Required]
        [Column(TypeName = "DATETIME2")]
        public DateTime Timestamp { get; set; }

        [Required]
        [StringLength(20)]
        [Column(TypeName = "NVARCHAR(20)")]
        public string Status { get; set; } = string.Empty;

        [Column(TypeName = "DECIMAL(18, 2)")]
        public decimal? Uptime { get; set; }

        [Column(TypeName = "DECIMAL(18, 2)")]
        public decimal? Downtime { get; set; }

        [Column(TypeName = "DECIMAL(18, 2)")]
        public decimal? CycleTime { get; set; }

        [Column(TypeName = "INT")]
        public int? Throughput { get; set; }

        [Column(TypeName = "DECIMAL(5, 2)")]
        public decimal? YieldRate { get; set; }

        [Column(TypeName = "DECIMAL(5, 2)")]
        public decimal? DefectRate { get; set; }

        [StringLength(200)]
        [Column(TypeName = "NVARCHAR(200)")]
        public string? DefectType { get; set; }

        // Navigation property
        public virtual Machine Machine { get; set; } = null!;
    }
}
