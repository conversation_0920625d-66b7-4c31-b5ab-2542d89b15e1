using TheDash.Models;

namespace TheDash.Services
{
    /// <summary>
    /// Service interface for dashboard operations
    /// Provides abstraction layer for KPI calculations and dashboard data
    /// </summary>
    public interface IDashboardService
    {
        /// <summary>
        /// Retrieves comprehensive dashboard overview data
        /// </summary>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="productionLine">Optional production line filter</param>
        /// <returns>Dashboard overview data with KPIs and machine information</returns>
        Task<DashboardOverviewData> GetDashboardOverviewAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null);

        /// <summary>
        /// Calculates total throughput across all machines
        /// </summary>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="productionLine">Optional production line filter</param>
        /// <returns>Total throughput value</returns>
        Task<int> GetTotalThroughputAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null);

        /// <summary>
        /// Calculates average yield rate across all machines
        /// </summary>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="productionLine">Optional production line filter</param>
        /// <returns>Average yield rate percentage</returns>
        Task<decimal> GetAverageYieldRateAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null);

        /// <summary>
        /// Calculates total downtime across all machines
        /// </summary>
        /// <param name="startDate">Optional start date filter</param>
        /// <param name="endDate">Optional end date filter</param>
        /// <param name="productionLine">Optional production line filter</param>
        /// <returns>Total downtime in hours</returns>
        Task<decimal> GetTotalDowntimeAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null);

        /// <summary>
        /// Retrieves all available production lines
        /// </summary>
        /// <returns>List of production line names</returns>
        Task<List<string>> GetProductionLinesAsync();

        /// <summary>
        /// Retrieves all available machine types
        /// </summary>
        /// <returns>List of machine type names</returns>
        Task<List<string>> GetMachineTypesAsync();
    }
}
