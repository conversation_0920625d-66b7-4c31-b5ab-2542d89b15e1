using System.ComponentModel.DataAnnotations;

namespace TheDash.Models
{
    public class EmulatorViewModel
    {
        public List<VirtualMachine> Machines { get; set; } = new List<VirtualMachine>();
        public VirtualMachine NewMachine { get; set; } = new VirtualMachine();
        public MachineDataUpdate DataUpdate { get; set; } = new MachineDataUpdate();
    }

    public class VirtualMachine
    {
        public int MachineId { get; set; }

        [Required]
        [StringLength(100)]
        public string MachineName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string MachineType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ProductionLine { get; set; } = string.Empty;

        public bool IsVirtual { get; set; } = true;

        // Current machine data
        public string Status { get; set; } = "Idle";
        public DateTime LastUpdated { get; set; } = DateTime.Now;
        public decimal? Uptime { get; set; }
        public decimal? Downtime { get; set; }
        public decimal? CycleTime { get; set; }
        public int? Throughput { get; set; }
        public decimal? YieldRate { get; set; }
        public decimal? DefectRate { get; set; }
        public string? DefectType { get; set; }
    }

    public class MachineDataUpdate
    {
        [Required]
        public int MachineId { get; set; }

        [Required(ErrorMessage = "Status is required")]
        [RegularExpression("^(Running|Down|Idle|Maintenance)$", ErrorMessage = "Status must be one of: Running, Down, Idle, Maintenance")]
        public string Status { get; set; } = string.Empty;

        [Range(0, 100, ErrorMessage = "Uptime must be between 0 and 100 percent")]
        public decimal? Uptime { get; set; }

        [Range(0, 100, ErrorMessage = "Downtime must be between 0 and 100 percent")]
        public decimal? Downtime { get; set; }

        [Range(0.1, 1000, ErrorMessage = "Cycle time must be between 0.1 and 1000 seconds")]
        public decimal? CycleTime { get; set; }

        [Range(0, 10000, ErrorMessage = "Throughput must be between 0 and 10000 units")]
        public int? Throughput { get; set; }

        [Range(0, 100, ErrorMessage = "Yield rate must be between 0 and 100 percent")]
        public decimal? YieldRate { get; set; }

        [Range(0, 100, ErrorMessage = "Defect rate must be between 0 and 100 percent")]
        public decimal? DefectRate { get; set; }

        [StringLength(200, ErrorMessage = "Defect type cannot exceed 200 characters")]
        public string? DefectType { get; set; }
    }

    public class CreateMachineRequest
    {
        [Required]
        [StringLength(100)]
        public string MachineName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string MachineType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ProductionLine { get; set; } = string.Empty;
    }

    public class UpdateMachineRequest
    {
        [Required]
        [StringLength(100)]
        public string MachineName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string MachineType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string ProductionLine { get; set; } = string.Empty;
    }

    // Response models
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
    }

    public class MachineStatusOption
    {
        public string Value { get; set; } = string.Empty;
        public string Text { get; set; } = string.Empty;

        public static List<MachineStatusOption> GetStatusOptions()
        {
            return new List<MachineStatusOption>
            {
                new MachineStatusOption { Value = "Running", Text = "Running" },
                new MachineStatusOption { Value = "Down", Text = "Down" },
                new MachineStatusOption { Value = "Idle", Text = "Idle" },
                new MachineStatusOption { Value = "Maintenance", Text = "Maintenance" }
            };
        }
    }
}
