using TheDash.Models;
using TheDash.Models.Entities;

namespace TheDash.Services
{
    /// <summary>
    /// Service interface for virtual machine emulator operations
    /// Provides abstraction layer for business logic and data access
    /// </summary>
    public interface IEmulatorService
    {
        /// <summary>
        /// Retrieves all virtual machines with their latest data
        /// </summary>
        /// <returns>List of virtual machines with current status and metrics</returns>
        Task<List<VirtualMachine>> GetVirtualMachinesAsync();

        /// <summary>
        /// Creates a new virtual machine
        /// </summary>
        /// <param name="request">Machine creation request</param>
        /// <returns>Created virtual machine</returns>
        Task<VirtualMachine> CreateMachineAsync(CreateMachineRequest request);

        /// <summary>
        /// Updates an existing machine's basic properties
        /// </summary>
        /// <param name="id">Machine ID</param>
        /// <param name="request">Update request</param>
        /// <returns>Updated virtual machine</returns>
        Task<VirtualMachine> UpdateMachineAsync(int id, UpdateMachineRequest request);

        /// <summary>
        /// Deletes a virtual machine and all its data
        /// </summary>
        /// <param name="id">Machine ID</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteMachineAsync(int id);

        /// <summary>
        /// Adds new data record for a machine
        /// </summary>
        /// <param name="id">Machine ID</param>
        /// <param name="dataUpdate">Machine data update</param>
        /// <returns>Updated virtual machine with new data</returns>
        Task<VirtualMachine> AddMachineDataAsync(int id, MachineDataUpdate dataUpdate);

        /// <summary>
        /// Checks if a machine name already exists
        /// </summary>
        /// <param name="machineName">Machine name to check</param>
        /// <param name="excludeId">Machine ID to exclude from check (for updates)</param>
        /// <returns>True if name exists</returns>
        Task<bool> MachineNameExistsAsync(string machineName, int? excludeId = null);

        /// <summary>
        /// Checks if a machine exists
        /// </summary>
        /// <param name="id">Machine ID</param>
        /// <returns>True if machine exists</returns>
        Task<bool> MachineExistsAsync(int id);
    }
}
