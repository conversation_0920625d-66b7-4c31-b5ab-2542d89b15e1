/* Virtual Machine Emulator Styles */

.emulator-container {
    padding: 20px 0;
}

.machine-status-running {
    background-color: #d4edda;
    color: #155724;
}

.machine-status-down {
    background-color: #f8d7da;
    color: #721c24;
}

.machine-status-idle {
    background-color: #fff3cd;
    color: #856404;
}

.machine-status-maintenance {
    background-color: #d1ecf1;
    color: #0c5460;
}

.kpi-input {
    min-width: 80px;
}

.status-select {
    min-width: 120px;
}

.machine-actions {
    white-space: nowrap;
}

.machine-actions .btn {
    margin-right: 2px;
}

.machine-actions .btn:last-child {
    margin-right: 0;
}

.card-header h5 {
    margin-bottom: 0;
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.alert {
    margin-bottom: 20px;
}

/* Real-time update indicators */
.updating {
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.updated {
    background-color: #d4edda !important;
    transition: background-color 0.5s ease;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 0.125em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .kpi-input,
    .status-select {
        min-width: 60px;
        font-size: 0.75rem;
    }
    
    .machine-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* Custom button styles */
.btn-outline-primary:hover,
.btn-outline-danger:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

/* Modal improvements */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* Success/Error states */
.success-highlight {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25) !important;
}

.error-highlight {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
}
