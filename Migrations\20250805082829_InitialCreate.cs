﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace TheDash.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Localizations",
                columns: table => new
                {
                    LocalizationId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Key = table.Column<string>(type: "NVARCHAR(200)", maxLength: 200, nullable: false),
                    Language = table.Column<string>(type: "NVARCHAR(10)", maxLength: 10, nullable: false),
                    Value = table.Column<string>(type: "NVARCHAR(MAX)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Localizations", x => x.LocalizationId);
                });

            migrationBuilder.CreateTable(
                name: "Machines",
                columns: table => new
                {
                    MachineId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MachineName = table.Column<string>(type: "NVARCHAR(100)", maxLength: 100, nullable: false),
                    MachineType = table.Column<string>(type: "NVARCHAR(50)", maxLength: 50, nullable: false),
                    ProductionLine = table.Column<string>(type: "NVARCHAR(50)", maxLength: 50, nullable: false),
                    IsVirtual = table.Column<bool>(type: "BIT", nullable: false, defaultValue: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Machines", x => x.MachineId);
                });

            migrationBuilder.CreateTable(
                name: "Alerts",
                columns: table => new
                {
                    AlertId = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MachineId = table.Column<int>(type: "int", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "DATETIME2", nullable: false),
                    AlertType = table.Column<string>(type: "NVARCHAR(50)", maxLength: 50, nullable: false),
                    Reason = table.Column<string>(type: "NVARCHAR(MAX)", nullable: false),
                    Status = table.Column<string>(type: "NVARCHAR(20)", maxLength: 20, nullable: false, defaultValue: "Pending"),
                    ResolvedTimestamp = table.Column<DateTime>(type: "DATETIME2", nullable: true),
                    ResolvedBy = table.Column<string>(type: "NVARCHAR(100)", maxLength: 100, nullable: true),
                    PredictionRisk = table.Column<double>(type: "FLOAT", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Alerts", x => x.AlertId);
                    table.ForeignKey(
                        name: "FK_Alerts_Machines_MachineId",
                        column: x => x.MachineId,
                        principalTable: "Machines",
                        principalColumn: "MachineId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MachineData",
                columns: table => new
                {
                    DataId = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MachineId = table.Column<int>(type: "int", nullable: false),
                    Timestamp = table.Column<DateTime>(type: "DATETIME2", nullable: false),
                    Status = table.Column<string>(type: "NVARCHAR(20)", maxLength: 20, nullable: false),
                    Uptime = table.Column<decimal>(type: "DECIMAL(18,2)", precision: 18, scale: 2, nullable: true),
                    Downtime = table.Column<decimal>(type: "DECIMAL(18,2)", precision: 18, scale: 2, nullable: true),
                    CycleTime = table.Column<decimal>(type: "DECIMAL(18,2)", precision: 18, scale: 2, nullable: true),
                    Throughput = table.Column<int>(type: "INT", nullable: true),
                    YieldRate = table.Column<decimal>(type: "DECIMAL(5,2)", precision: 5, scale: 2, nullable: true),
                    DefectRate = table.Column<decimal>(type: "DECIMAL(5,2)", precision: 5, scale: 2, nullable: true),
                    DefectType = table.Column<string>(type: "NVARCHAR(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MachineData", x => x.DataId);
                    table.ForeignKey(
                        name: "FK_MachineData_Machines_MachineId",
                        column: x => x.MachineId,
                        principalTable: "Machines",
                        principalColumn: "MachineId",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                table: "Machines",
                columns: new[] { "MachineId", "IsVirtual", "MachineName", "MachineType", "ProductionLine" },
                values: new object[,]
                {
                    { 1, true, "Assembly Line 1", "Assembly", "Line A" },
                    { 2, true, "Quality Check 1", "QC", "Line A" },
                    { 3, true, "Packaging Unit 1", "Packaging", "Line B" }
                });

            migrationBuilder.InsertData(
                table: "MachineData",
                columns: new[] { "DataId", "CycleTime", "DefectRate", "DefectType", "Downtime", "MachineId", "Status", "Throughput", "Timestamp", "Uptime", "YieldRate" },
                values: new object[,]
                {
                    { 1L, 2.5m, 2.1m, "Minor scratches", 4.5m, 1, "Running", 150, new DateTime(2025, 1, 8, 11, 30, 0, 0, DateTimeKind.Unspecified), 95.5m, 95.5m },
                    { 2L, 1.8m, 0.5m, null, 11.8m, 2, "Idle", 80, new DateTime(2025, 1, 8, 11, 45, 0, 0, DateTimeKind.Unspecified), 88.2m, 98.2m },
                    { 3L, 3.2m, 1.2m, "Packaging misalignment", 7.9m, 3, "Running", 120, new DateTime(2025, 1, 8, 11, 55, 0, 0, DateTimeKind.Unspecified), 92.1m, 96.8m }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Alerts_MachineId_Timestamp",
                table: "Alerts",
                columns: new[] { "MachineId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_Localizations_Key_Language",
                table: "Localizations",
                columns: new[] { "Key", "Language" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MachineData_MachineId_Timestamp",
                table: "MachineData",
                columns: new[] { "MachineId", "Timestamp" });

            migrationBuilder.CreateIndex(
                name: "IX_Machines_MachineName",
                table: "Machines",
                column: "MachineName",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Alerts");

            migrationBuilder.DropTable(
                name: "Localizations");

            migrationBuilder.DropTable(
                name: "MachineData");

            migrationBuilder.DropTable(
                name: "Machines");
        }
    }
}
