using Microsoft.EntityFrameworkCore;
using TheDash.Data;
using TheDash.Models;
using TheDash.Models.Entities;

namespace TheDash.Services
{
    /// <summary>
    /// Service implementation for dashboard operations
    /// Encapsulates business logic for KPI calculations and dashboard data
    /// </summary>
    public class DashboardService : IDashboardService
    {
        private readonly ApplicationDbContext _context;

        public DashboardService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<DashboardOverviewData> GetDashboardOverviewAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null)
        {
            // Set default date range if not provided (last 24 hours)
            var defaultStartDate = startDate ?? DateTime.Now.AddDays(-1);
            var defaultEndDate = endDate ?? DateTime.Now;

            // Get all required data sequentially to avoid DbContext concurrency issues
            var totalThroughput = await GetTotalThroughputAsync(startDate, endDate, productionLine);
            var averageYieldRate = await GetAverageYieldRateAsync(startDate, endDate, productionLine);
            var totalDowntime = await GetTotalDowntimeAsync(startDate, endDate, productionLine);
            var machines = await GetDashboardMachinesAsync(productionLine);
            var productionLines = await GetProductionLinesAsync();
            var machineTypes = await GetMachineTypesAsync();
            var trendData = await GetTrendDataAsync(defaultStartDate, defaultEndDate, productionLine);
            var statusCounts = await GetStatusCountsAsync(productionLine);

            return new DashboardOverviewData
            {
                TotalThroughput = totalThroughput,
                AverageYieldRate = averageYieldRate,
                TotalDowntime = totalDowntime,
                Machines = machines,
                ProductionLines = productionLines,
                MachineTypes = machineTypes,
                TrendData = trendData,
                StatusCounts = statusCounts
            };
        }

        public async Task<int> GetTotalThroughputAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null)
        {
            var query = _context.MachineData
                .Include(md => md.Machine)
                .AsQueryable();

            // Apply filters
            query = ApplyFilters(query, startDate, endDate, productionLine);

            // Get latest data for each machine and sum throughput
            var latestData = await query
                .GroupBy(md => md.MachineId)
                .Select(g => g.OrderByDescending(md => md.Timestamp).First())
                .AsNoTracking()
                .ToListAsync();

            return latestData.Sum(md => md.Throughput ?? 0);
        }

        public async Task<decimal> GetAverageYieldRateAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null)
        {
            var query = _context.MachineData
                .Include(md => md.Machine)
                .AsQueryable();

            // Apply filters
            query = ApplyFilters(query, startDate, endDate, productionLine);

            // Get latest data for each machine and calculate average yield rate
            var latestData = await query
                .GroupBy(md => md.MachineId)
                .Select(g => g.OrderByDescending(md => md.Timestamp).First())
                .AsNoTracking()
                .ToListAsync();

            if (!latestData.Any())
                return 0;

            return latestData.Average(md => md.YieldRate ?? 0);
        }

        public async Task<decimal> GetTotalDowntimeAsync(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null)
        {
            var query = _context.MachineData
                .Include(md => md.Machine)
                .AsQueryable();

            // Apply filters
            query = ApplyFilters(query, startDate, endDate, productionLine);

            // Get latest data for each machine and sum downtime
            var latestData = await query
                .GroupBy(md => md.MachineId)
                .Select(g => g.OrderByDescending(md => md.Timestamp).First())
                .AsNoTracking()
                .ToListAsync();

            return latestData.Sum(md => md.Downtime ?? 0);
        }

        public async Task<List<string>> GetProductionLinesAsync()
        {
            return await _context.Machines
                .Select(m => m.ProductionLine)
                .Distinct()
                .OrderBy(pl => pl)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<List<string>> GetMachineTypesAsync()
        {
            return await _context.Machines
                .Select(m => m.MachineType)
                .Distinct()
                .OrderBy(mt => mt)
                .AsNoTracking()
                .ToListAsync();
        }

        private async Task<List<DashboardMachine>> GetDashboardMachinesAsync(string? productionLine = null)
        {
            var query = _context.Machines
                .Include(m => m.MachineData)
                .AsQueryable();

            if (!string.IsNullOrEmpty(productionLine))
            {
                query = query.Where(m => m.ProductionLine == productionLine);
            }

            var machines = await query
                .AsNoTracking()
                .ToListAsync();

            return machines.Select(machine =>
            {
                var latestData = machine.MachineData
                    .OrderByDescending(md => md.Timestamp)
                    .FirstOrDefault();

                return new DashboardMachine
                {
                    MachineId = machine.MachineId,
                    MachineName = machine.MachineName,
                    MachineType = machine.MachineType,
                    ProductionLine = machine.ProductionLine,
                    Status = latestData?.Status ?? "Unknown",
                    Throughput = latestData?.Throughput,
                    YieldRate = latestData?.YieldRate,
                    Downtime = latestData?.Downtime,
                    LastUpdated = latestData?.Timestamp
                };
            }).ToList();
        }

        private async Task<TrendData> GetTrendDataAsync(DateTime startDate, DateTime endDate, string? productionLine = null)
        {
            var query = _context.MachineData
                .Include(md => md.Machine)
                .Where(md => md.Timestamp >= startDate && md.Timestamp <= endDate)
                .AsQueryable();

            if (!string.IsNullOrEmpty(productionLine))
            {
                query = query.Where(md => md.Machine.ProductionLine == productionLine);
            }

            // Group by hour for trend data
            var hourlyData = await query
                .GroupBy(md => new { 
                    Hour = md.Timestamp.Date.AddHours(md.Timestamp.Hour) 
                })
                .Select(g => new {
                    Hour = g.Key.Hour,
                    TotalThroughput = g.Sum(md => md.Throughput ?? 0),
                    AverageYieldRate = g.Average(md => md.YieldRate ?? 0)
                })
                .OrderBy(x => x.Hour)
                .AsNoTracking()
                .ToListAsync();

            return new TrendData
            {
                Labels = hourlyData.Select(x => x.Hour.ToString("HH:mm")).ToList(),
                Throughput = hourlyData.Select(x => x.TotalThroughput).ToList(),
                YieldRate = hourlyData.Select(x => x.AverageYieldRate).ToList()
            };
        }

        private async Task<StatusCounts> GetStatusCountsAsync(string? productionLine = null)
        {
            var query = _context.MachineData
                .Include(md => md.Machine)
                .AsQueryable();

            if (!string.IsNullOrEmpty(productionLine))
            {
                query = query.Where(md => md.Machine.ProductionLine == productionLine);
            }

            // Get latest status for each machine
            var latestStatuses = await query
                .GroupBy(md => md.MachineId)
                .Select(g => g.OrderByDescending(md => md.Timestamp).First().Status)
                .AsNoTracking()
                .ToListAsync();

            return new StatusCounts
            {
                Running = latestStatuses.Count(s => s == "Running"),
                Down = latestStatuses.Count(s => s == "Down"),
                Idle = latestStatuses.Count(s => s == "Idle"),
                Maintenance = latestStatuses.Count(s => s == "Maintenance")
            };
        }

        private IQueryable<MachineData> ApplyFilters(IQueryable<MachineData> query, DateTime? startDate, DateTime? endDate, string? productionLine)
        {
            if (startDate.HasValue)
            {
                query = query.Where(md => md.Timestamp >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(md => md.Timestamp <= endDate.Value);
            }

            if (!string.IsNullOrEmpty(productionLine))
            {
                query = query.Where(md => md.Machine.ProductionLine == productionLine);
            }

            return query;
        }
    }
}
