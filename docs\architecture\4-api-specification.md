# 4. API Specification

**API Version:** 2.0
**Base URL:** `https://[your-api-domain]/api`
**Data Format:** JSON

**API 2.1: Get Production Line Overview Data**
- **HTTP Method:** `GET`
- **Endpoint:** `/dashboard/overview`
- **Description:** Retrieves overall KPI overview data for all production lines.
- **Request Parameters:** `startDate`, `endDate`, `productionLine` (optional)

**API 3.1: Get Latest Alerts List**
- **HTTP Method:** `GET`
- **Endpoint:** `/alerts/latest`
- **Description:** Retrieves the latest alerts from the system.

**API 2.0.1 [Added]: Trigger AI Model Prediction**
- **HTTP Method:** `POST`
- **Endpoint:** `/predictive/predict`
- **Description:** Sends the latest machine data to the AI/ML service and retrieves the failure prediction result.
- **Request Example:**
    ```json
    {
      "machineId": 1,
      "data": [...] // Machine metric data
    }
    ```
- **Response Example:**
    ```json
    {
      "machineId": 1,
      "predictionScore": 0.85, // Failure risk score
      "message": "High risk of failure detected in next 24 hours."
    }
    ```
---

