using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TheDash.Models.Entities
{
    [Table("Alerts")]
    public class Alert
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int AlertId { get; set; }

        [Required]
        [ForeignKey("Machine")]
        public int MachineId { get; set; }

        [Required]
        [Column(TypeName = "DATETIME2")]
        public DateTime Timestamp { get; set; }

        [Required]
        [StringLength(50)]
        [Column(TypeName = "NVARCHAR(50)")]
        public string AlertType { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "NVARCHAR(MAX)")]
        public string Reason { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        [Column(TypeName = "NVARCHAR(20)")]
        public string Status { get; set; } = "Pending";

        [Column(TypeName = "DATETIME2")]
        public DateTime? ResolvedTimestamp { get; set; }

        [StringLength(100)]
        [Column(TypeName = "NVARCHAR(100)")]
        public string? ResolvedBy { get; set; }

        [Column(TypeName = "FLOAT")]
        public float? PredictionRisk { get; set; }

        // Navigation property
        public virtual Machine Machine { get; set; } = null!;
    }
}
