# 3. Database Architecture Design

### 3.1 Machines Table (Machine Information)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| MachineId | INT | PRIMARY KEY, IDENTITY(1,1) | Unique machine ID. |
| MachineName | NVARCHAR(100) | NOT NULL, UNIQUE | Unique name of the machine. |
| MachineType | NVARCHAR(50) | NOT NULL | Type of the machine. |
| ProductionLine | NVARCHAR(50) | NOT NULL | Production line the machine belongs to. |
| IsVirtual | BIT | NOT NULL, DEFAULT(1) | Flag indicating if the machine is virtual. |

### 3.2 MachineData Table (Real-time Machine Data)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| DataId | BIGINT | PRIMARY KEY CLUSTERED | Unique data record ID, used as the clustered index. |
| MachineId | INT | FOREIGN KEY REFERENCES Machines(MachineId) | Foreign key to the Machines table. |
| Timestamp | DATETIME2 | NOT NULL | The timestamp of data collection. |
| Status | NVARCHAR(20) | NOT NULL | The machine's status. |
| Uptime | DECIMAL(18, 2) | | Uptime. |
| Downtime | DECIMAL(18, 2) | | Downtime. |
| CycleTime | DECIMAL(18, 2) | | Cycle time. |
| Throughput | INT | | Throughput. |
| YieldRate | DECIMAL(5, 2) | | Yield rate. |
| DefectRate | DECIMAL(5, 2) | | Defect rate. |
| DefectType | NVARCHAR(200) | | Type of defects. |

**Indexing Strategy:**
- Create a non-clustered index on `(MachineId, Timestamp)` to optimize queries for a specific machine's data within a time range.

### 3.3 Alerts Table (Alert Records)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| AlertId | INT | PRIMARY KEY IDENTITY(1,1) | Unique alert ID. |
| MachineId | INT | FOREIGN KEY REFERENCES Machines(MachineId) | Foreign key to the Machines table. |
| Timestamp | DATETIME2 | NOT NULL | The timestamp when the alert was triggered. |
| AlertType | NVARCHAR(50) | NOT NULL | The type of alert. |
| Reason | NVARCHAR(MAX) | NOT NULL | The detailed reason for the alert. |
| Status | NVARCHAR(20) | NOT NULL, DEFAULT('Pending') | The status of the alert. |
| ResolvedTimestamp | DATETIME2 | NULL | The timestamp when the alert was resolved. |
| ResolvedBy | NVARCHAR(100) | NULL | The user who resolved the alert. |
| **[Added]** PredictionRisk | FLOAT | NULL | AI model's predicted failure risk score. |

**Indexing Strategy:**
- Create a non-clustered index on `(MachineId, Timestamp)` to quickly query alert records for a specific machine within a time range.

### 3.4 Localizations Table (Localization Text)

| Column Name | Data Type | Constraints | Description |
|---|---|---|---|
| LocalizationId | INT | PRIMARY KEY IDENTITY(1,1) | Unique localization ID. |
| Key | NVARCHAR(200) | NOT NULL | The unique key for the text string. |
| Language | NVARCHAR(10) | NOT NULL | The language code. |
| Value | NVARCHAR(MAX) | NOT NULL | The translated text for the corresponding language. |

**Indexing Strategy:**
- Create a unique non-clustered index on `(Key, Language)` to ensure fast and unique lookups of translated text.

