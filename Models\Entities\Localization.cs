using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TheDash.Models.Entities
{
    [Table("Localizations")]
    public class Localization
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int LocalizationId { get; set; }

        [Required]
        [StringLength(200)]
        [Column(TypeName = "NVARCHAR(200)")]
        public string Key { get; set; } = string.Empty;

        [Required]
        [StringLength(10)]
        [Column(TypeName = "NVARCHAR(10)")]
        public string Language { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "NVARCHAR(MAX)")]
        public string Value { get; set; } = string.Empty;
    }
}
