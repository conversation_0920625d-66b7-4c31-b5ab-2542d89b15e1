using Microsoft.EntityFrameworkCore;
using TheDash.Data;
using TheDash.Models;
using TheDash.Models.Entities;

namespace TheDash.Services
{
    /// <summary>
    /// Service implementation for virtual machine emulator operations
    /// Encapsulates business logic and data access patterns
    /// </summary>
    public class EmulatorService : IEmulatorService
    {
        private readonly ApplicationDbContext _context;

        public EmulatorService(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<List<VirtualMachine>> GetVirtualMachinesAsync()
        {
            var machines = await _context.Machines
                .Where(m => m.IsVirtual)
                .Include(m => m.MachineData)
                .AsNoTracking() // Performance optimization for read-only operations
                .ToListAsync();

            return machines.Select(MapToVirtualMachine).ToList();
        }

        public async Task<VirtualMachine> CreateMachineAsync(CreateMachineRequest request)
        {
            if (await MachineNameExistsAsync(request.MachineName))
            {
                throw new InvalidOperationException("Machine name already exists");
            }

            // Check if we're already in a transaction (e.g., during testing)
            var currentTransaction = _context.Database.CurrentTransaction;
            if (currentTransaction != null)
            {
                // Already in a transaction, don't create a new one
                var newMachine = new Machine
                {
                    MachineName = request.MachineName,
                    MachineType = request.MachineType,
                    ProductionLine = request.ProductionLine,
                    IsVirtual = true
                };

                _context.Machines.Add(newMachine);
                await _context.SaveChangesAsync();

                // Create initial machine data record
                var initialData = new MachineData
                {
                    MachineId = newMachine.MachineId,
                    Timestamp = DateTime.UtcNow,
                    Status = "Idle"
                };

                _context.MachineData.Add(initialData);
                await _context.SaveChangesAsync();

                // Return the created machine with initial data
                newMachine.MachineData = new List<MachineData> { initialData };
                return MapToVirtualMachine(newMachine);
            }
            else
            {
                // Not in a transaction, create our own
                using var transaction = await _context.Database.BeginTransactionAsync();
                try
                {
                    var newMachine = new Machine
                    {
                        MachineName = request.MachineName,
                        MachineType = request.MachineType,
                        ProductionLine = request.ProductionLine,
                        IsVirtual = true
                    };

                    _context.Machines.Add(newMachine);
                    await _context.SaveChangesAsync();

                    // Create initial machine data record
                    var initialData = new MachineData
                    {
                        MachineId = newMachine.MachineId,
                        Timestamp = DateTime.UtcNow,
                        Status = "Idle"
                    };

                    _context.MachineData.Add(initialData);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();

                    // Return the created machine with initial data
                    newMachine.MachineData = new List<MachineData> { initialData };
                    return MapToVirtualMachine(newMachine);
                }
                catch
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        public async Task<VirtualMachine> UpdateMachineAsync(int id, UpdateMachineRequest request)
        {
            var machine = await _context.Machines.FindAsync(id);
            if (machine == null)
            {
                throw new InvalidOperationException("Machine not found");
            }

            if (await MachineNameExistsAsync(request.MachineName, id))
            {
                throw new InvalidOperationException("Machine name already exists");
            }

            machine.MachineName = request.MachineName;
            machine.MachineType = request.MachineType;
            machine.ProductionLine = request.ProductionLine;

            await _context.SaveChangesAsync();

            // Load with latest data for return
            await _context.Entry(machine)
                .Collection(m => m.MachineData)
                .LoadAsync();

            return MapToVirtualMachine(machine);
        }

        public async Task<bool> DeleteMachineAsync(int id)
        {
            var machine = await _context.Machines.FindAsync(id);
            if (machine == null)
            {
                return false;
            }

            _context.Machines.Remove(machine);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<VirtualMachine> AddMachineDataAsync(int id, MachineDataUpdate dataUpdate)
        {
            var machine = await _context.Machines
                .Include(m => m.MachineData)
                .FirstOrDefaultAsync(m => m.MachineId == id);

            if (machine == null)
            {
                throw new InvalidOperationException("Machine not found");
            }

            var machineData = new MachineData
            {
                MachineId = id,
                Timestamp = DateTime.UtcNow,
                Status = dataUpdate.Status,
                Uptime = dataUpdate.Uptime,
                Downtime = dataUpdate.Downtime,
                CycleTime = dataUpdate.CycleTime,
                Throughput = dataUpdate.Throughput,
                YieldRate = dataUpdate.YieldRate,
                DefectRate = dataUpdate.DefectRate,
                DefectType = dataUpdate.DefectType
            };

            _context.MachineData.Add(machineData);
            await _context.SaveChangesAsync();

            // Add the new data to the machine's collection for mapping
            machine.MachineData.Add(machineData);
            return MapToVirtualMachine(machine);
        }

        public async Task<bool> MachineNameExistsAsync(string machineName, int? excludeId = null)
        {
            var query = _context.Machines.Where(m => m.MachineName == machineName);
            
            if (excludeId.HasValue)
            {
                query = query.Where(m => m.MachineId != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        public async Task<bool> MachineExistsAsync(int id)
        {
            return await _context.Machines.AnyAsync(m => m.MachineId == id);
        }

        /// <summary>
        /// Maps a Machine entity to a VirtualMachine view model
        /// Encapsulates the mapping logic for consistency
        /// </summary>
        private static VirtualMachine MapToVirtualMachine(Machine machine)
        {
            var latestData = machine.MachineData?
                .OrderByDescending(d => d.Timestamp)
                .FirstOrDefault();

            return new VirtualMachine
            {
                MachineId = machine.MachineId,
                MachineName = machine.MachineName,
                MachineType = machine.MachineType,
                ProductionLine = machine.ProductionLine,
                IsVirtual = machine.IsVirtual,
                Status = latestData?.Status ?? "Unknown",
                LastUpdated = latestData?.Timestamp ?? DateTime.UtcNow,
                Uptime = latestData?.Uptime,
                Downtime = latestData?.Downtime,
                CycleTime = latestData?.CycleTime,
                Throughput = latestData?.Throughput,
                YieldRate = latestData?.YieldRate,
                DefectRate = latestData?.DefectRate,
                DefectType = latestData?.DefectType
            };
        }
    }
}
