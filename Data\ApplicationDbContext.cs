using Microsoft.EntityFrameworkCore;
using TheDash.Models.Entities;

namespace TheDash.Data
{
    public class ApplicationDbContext : DbContext
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets
        public DbSet<Machine> Machines { get; set; }
        public DbSet<MachineData> MachineData { get; set; }
        public DbSet<Alert> Alerts { get; set; }
        public DbSet<Localization> Localizations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure Machine entity
            modelBuilder.Entity<Machine>(entity =>
            {
                entity.HasKey(e => e.MachineId);
                entity.HasIndex(e => e.MachineName).IsUnique();
                entity.Property(e => e.IsVirtual).HasDefaultValue(true);
            });

            // Configure MachineData entity
            modelBuilder.Entity<MachineData>(entity =>
            {
                entity.HasKey(e => e.DataId);
                
                // Create non-clustered index on (MachineId, Timestamp) for optimization
                entity.HasIndex(e => new { e.MachineId, e.Timestamp })
                      .HasDatabaseName("IX_MachineData_MachineId_Timestamp");

                // Configure foreign key relationship
                entity.HasOne(d => d.Machine)
                      .WithMany(p => p.MachineData)
                      .HasForeignKey(d => d.MachineId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Configure precision for decimal columns
                entity.Property(e => e.Uptime).HasPrecision(18, 2);
                entity.Property(e => e.Downtime).HasPrecision(18, 2);
                entity.Property(e => e.CycleTime).HasPrecision(18, 2);
                entity.Property(e => e.YieldRate).HasPrecision(5, 2);
                entity.Property(e => e.DefectRate).HasPrecision(5, 2);
            });

            // Configure Alert entity
            modelBuilder.Entity<Alert>(entity =>
            {
                entity.HasKey(e => e.AlertId);
                
                // Create non-clustered index on (MachineId, Timestamp) for optimization
                entity.HasIndex(e => new { e.MachineId, e.Timestamp })
                      .HasDatabaseName("IX_Alerts_MachineId_Timestamp");

                // Configure foreign key relationship
                entity.HasOne(d => d.Machine)
                      .WithMany(p => p.Alerts)
                      .HasForeignKey(d => d.MachineId)
                      .OnDelete(DeleteBehavior.Cascade);

                // Set default value for Status
                entity.Property(e => e.Status).HasDefaultValue("Pending");
            });

            // Configure Localization entity
            modelBuilder.Entity<Localization>(entity =>
            {
                entity.HasKey(e => e.LocalizationId);
                
                // Create unique non-clustered index on (Key, Language)
                entity.HasIndex(e => new { e.Key, e.Language })
                      .IsUnique()
                      .HasDatabaseName("IX_Localizations_Key_Language");
            });

            // Seed data for virtual machines
            SeedData(modelBuilder);
        }

        private static void SeedData(ModelBuilder modelBuilder)
        {
            // Seed virtual machines
            modelBuilder.Entity<Machine>().HasData(
                new Machine
                {
                    MachineId = 1,
                    MachineName = "Assembly Line 1",
                    MachineType = "Assembly",
                    ProductionLine = "Line A",
                    IsVirtual = true
                },
                new Machine
                {
                    MachineId = 2,
                    MachineName = "Quality Check 1",
                    MachineType = "QC",
                    ProductionLine = "Line A",
                    IsVirtual = true
                },
                new Machine
                {
                    MachineId = 3,
                    MachineName = "Packaging Unit 1",
                    MachineType = "Packaging",
                    ProductionLine = "Line B",
                    IsVirtual = true
                }
            );

            // Seed initial machine data
            var baseTime = new DateTime(2025, 1, 8, 12, 0, 0);
            modelBuilder.Entity<MachineData>().HasData(
                new MachineData
                {
                    DataId = 1,
                    MachineId = 1,
                    Timestamp = baseTime.AddMinutes(-30),
                    Status = "Running",
                    Uptime = 95.5m,
                    Downtime = 4.5m,
                    CycleTime = 2.5m,
                    Throughput = 150,
                    YieldRate = 95.5m,
                    DefectRate = 2.1m,
                    DefectType = "Minor scratches"
                },
                new MachineData
                {
                    DataId = 2,
                    MachineId = 2,
                    Timestamp = baseTime.AddMinutes(-15),
                    Status = "Idle",
                    Uptime = 88.2m,
                    Downtime = 11.8m,
                    CycleTime = 1.8m,
                    Throughput = 80,
                    YieldRate = 98.2m,
                    DefectRate = 0.5m,
                    DefectType = null
                },
                new MachineData
                {
                    DataId = 3,
                    MachineId = 3,
                    Timestamp = baseTime.AddMinutes(-5),
                    Status = "Running",
                    Uptime = 92.1m,
                    Downtime = 7.9m,
                    CycleTime = 3.2m,
                    Throughput = 120,
                    YieldRate = 96.8m,
                    DefectRate = 1.2m,
                    DefectType = "Packaging misalignment"
                }
            );
        }
    }
}
