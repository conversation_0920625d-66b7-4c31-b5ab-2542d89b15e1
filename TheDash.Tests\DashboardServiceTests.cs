using Microsoft.EntityFrameworkCore;
using TheDash.Data;
using TheDash.Models.Entities;
using TheDash.Services;

namespace TheDash.Tests;

public class DashboardServiceTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly DashboardService _service;
    private readonly string _testDbName;

    public DashboardServiceTests()
    {
        // Use unique database for each test run
        _testDbName = $"TheDashDb_Service_Test_{Guid.NewGuid():N}";
        var connectionString = $"Server=.\\SQLEXPRESS;Database={_testDbName};Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;

        _context = new ApplicationDbContext(options);

        // Ensure database is created
        _context.Database.EnsureCreated();

        // Seed test data
        SeedTestData();

        _service = new DashboardService(_context);
    }

    private void SeedTestData()
    {
        // Clear any existing data
        _context.MachineData.RemoveRange(_context.MachineData);
        _context.Machines.RemoveRange(_context.Machines);
        _context.SaveChanges();

        // Add test machines
        var machines = new List<Machine>
        {
            new Machine { MachineName = "TestMachine1", MachineType = "TypeA", ProductionLine = "Line1", IsVirtual = true },
            new Machine { MachineName = "TestMachine2", MachineType = "TypeB", ProductionLine = "Line1", IsVirtual = true },
            new Machine { MachineName = "TestMachine3", MachineType = "TypeA", ProductionLine = "Line2", IsVirtual = true }
        };

        _context.Machines.AddRange(machines);
        _context.SaveChanges();

        // Add test machine data
        var machineData = new List<MachineData>
        {
            new MachineData 
            { 
                MachineId = machines[0].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Running",
                Throughput = 100,
                YieldRate = 95.5m,
                Downtime = 0.5m
            },
            new MachineData 
            { 
                MachineId = machines[1].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Down",
                Throughput = 0,
                YieldRate = 0,
                Downtime = 2.0m
            },
            new MachineData 
            { 
                MachineId = machines[2].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Running",
                Throughput = 150,
                YieldRate = 98.0m,
                Downtime = 0.1m
            }
        };

        _context.MachineData.AddRange(machineData);
        _context.SaveChanges();
    }

    [Fact]
    public async Task GetTotalThroughputAsync_ReturnsCorrectSum()
    {
        // Act
        var result = await _service.GetTotalThroughputAsync();

        // Assert
        Assert.Equal(250, result); // 100 + 0 + 150
    }

    [Fact]
    public async Task GetAverageYieldRateAsync_ReturnsCorrectAverage()
    {
        // Act
        var result = await _service.GetAverageYieldRateAsync();

        // Assert
        var expected = (95.5m + 0 + 98.0m) / 3; // Average of all yield rates
        Assert.Equal(expected, result, 2); // 2 decimal places precision
    }

    [Fact]
    public async Task GetTotalDowntimeAsync_ReturnsCorrectSum()
    {
        // Act
        var result = await _service.GetTotalDowntimeAsync();

        // Assert
        Assert.Equal(2.6m, result); // 0.5 + 2.0 + 0.1
    }

    [Fact]
    public async Task GetProductionLinesAsync_ReturnsDistinctLines()
    {
        // Act
        var result = await _service.GetProductionLinesAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains("Line1", result);
        Assert.Contains("Line2", result);
    }

    [Fact]
    public async Task GetMachineTypesAsync_ReturnsDistinctTypes()
    {
        // Act
        var result = await _service.GetMachineTypesAsync();

        // Assert
        Assert.Equal(2, result.Count);
        Assert.Contains("TypeA", result);
        Assert.Contains("TypeB", result);
    }

    [Fact]
    public async Task GetDashboardOverviewAsync_ReturnsCompleteData()
    {
        // Act
        var result = await _service.GetDashboardOverviewAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(250, result.TotalThroughput);
        Assert.True(result.AverageYieldRate > 0);
        Assert.Equal(2.6m, result.TotalDowntime);
        Assert.Equal(3, result.Machines.Count);
        Assert.Equal(2, result.ProductionLines.Count);
        Assert.Equal(2, result.MachineTypes.Count);
        Assert.NotNull(result.TrendData);
        Assert.NotNull(result.StatusCounts);
    }

    [Fact]
    public async Task GetDashboardOverviewAsync_WithProductionLineFilter_ReturnsFilteredData()
    {
        // Act
        var result = await _service.GetDashboardOverviewAsync(productionLine: "Line1");

        // Assert
        Assert.NotNull(result);
        Assert.Equal(100, result.TotalThroughput); // Only running machine from Line1
        Assert.Equal(2, result.Machines.Count); // Only machines from Line1
    }

    [Fact]
    public async Task GetDashboardOverviewAsync_WithDateRange_ReturnsFilteredData()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-1);
        var endDate = DateTime.Now;

        // Act
        var result = await _service.GetDashboardOverviewAsync(startDate, endDate);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.TotalThroughput >= 0);
    }

    [Fact]
    public async Task GetTotalThroughputAsync_WithProductionLineFilter_ReturnsFilteredSum()
    {
        // Act
        var result = await _service.GetTotalThroughputAsync(productionLine: "Line1");

        // Assert
        Assert.Equal(100, result); // Only running machine from Line1
    }

    [Fact]
    public async Task GetAverageYieldRateAsync_WithProductionLineFilter_ReturnsFilteredAverage()
    {
        // Act
        var result = await _service.GetAverageYieldRateAsync(productionLine: "Line2");

        // Assert
        Assert.Equal(98.0m, result); // Only one machine in Line2
    }

    [Fact]
    public async Task Constructor_WithNullContext_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => new DashboardService(null!));
    }

    public void Dispose()
    {
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }
}
