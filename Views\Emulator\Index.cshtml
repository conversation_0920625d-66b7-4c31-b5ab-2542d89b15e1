@{
    ViewData["Title"] = "Virtual Machine Emulator";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/css/emulator.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">Virtual Machine Emulator</h1>
            <p class="text-muted">Development and testing tool for simulating production data without real hardware.</p>
        </div>
    </div>

    <!-- Machine Management Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Virtual Machines</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMachineModal">
                        <i class="fas fa-plus"></i> Add Machine
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped" id="machinesTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Production Line</th>
                                    <th>Status</th>
                                    <th>Throughput</th>
                                    <th>Yield Rate (%)</th>
                                    <th>Cycle Time</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Machine data will be populated via JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Integration Test Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Dashboard Integration Test</h5>
                    <button type="button" class="btn btn-info btn-sm" onclick="emulator.testDashboardIntegration()">
                        <i class="fas fa-sync"></i> Test Dashboard Data
                    </button>
                </div>
                <div class="card-body">
                    <p class="text-muted">This section simulates how the main dashboard would receive real-time data from the emulator.</p>
                    <div id="dashboardTestResults" class="mt-3">
                        <!-- Dashboard test results will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Metrics Update Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick KPI Update</h5>
                </div>
                <div class="card-body">
                    <form id="kpiUpdateForm">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="machineSelect" class="form-label">Select Machine</label>
                                <select class="form-select" id="machineSelect" required>
                                    <option value="">Choose a machine...</option>
                                    <!-- Options populated via JavaScript -->
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="statusSelect" class="form-label">Status</label>
                                <select class="form-select" id="statusSelect" required>
                                    <option value="">Select status...</option>
                                    <option value="Running">Running</option>
                                    <option value="Down">Down</option>
                                    <option value="Idle">Idle</option>
                                    <option value="Maintenance">Maintenance</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="throughputInput" class="form-label">Throughput</label>
                                <input type="number" class="form-control" id="throughputInput" min="0" step="1">
                            </div>
                            <div class="col-md-2">
                                <label for="yieldRateInput" class="form-label">Yield Rate (%)</label>
                                <input type="number" class="form-control" id="yieldRateInput" min="0" max="100" step="0.01">
                            </div>
                            <div class="col-md-2">
                                <label for="cycleTimeInput" class="form-label">Cycle Time</label>
                                <input type="number" class="form-control" id="cycleTimeInput" min="0" step="0.01">
                            </div>
                            <div class="col-md-1 d-flex align-items-end">
                                <button type="submit" class="btn btn-success w-100">Update</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Machine Modal -->
<div class="modal fade" id="addMachineModal" tabindex="-1" aria-labelledby="addMachineModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMachineModalLabel">Add Virtual Machine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addMachineForm">
                    <div class="mb-3">
                        <label for="machineNameInput" class="form-label">Machine Name</label>
                        <input type="text" class="form-control" id="machineNameInput" required>
                    </div>
                    <div class="mb-3">
                        <label for="machineTypeInput" class="form-label">Machine Type</label>
                        <input type="text" class="form-control" id="machineTypeInput" required>
                    </div>
                    <div class="mb-3">
                        <label for="productionLineInput" class="form-label">Production Line</label>
                        <input type="text" class="form-control" id="productionLineInput" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveMachineBtn">Save Machine</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Machine Modal -->
<div class="modal fade" id="editMachineModal" tabindex="-1" aria-labelledby="editMachineModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editMachineModalLabel">Edit Virtual Machine</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editMachineForm">
                    <input type="hidden" id="editMachineId">
                    <div class="mb-3">
                        <label for="editMachineNameInput" class="form-label">Machine Name</label>
                        <input type="text" class="form-control" id="editMachineNameInput" required>
                    </div>
                    <div class="mb-3">
                        <label for="editMachineTypeInput" class="form-label">Machine Type</label>
                        <input type="text" class="form-control" id="editMachineTypeInput" required>
                    </div>
                    <div class="mb-3">
                        <label for="editProductionLineInput" class="form-label">Production Line</label>
                        <input type="text" class="form-control" id="editProductionLineInput" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="updateMachineBtn">Update Machine</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/emulator.js"></script>
}
