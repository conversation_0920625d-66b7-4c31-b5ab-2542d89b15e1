﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using TheDash.Data;

#nullable disable

namespace TheDash.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    partial class ApplicationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("TheDash.Models.Entities.Alert", b =>
                {
                    b.Property<int>("AlertId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("AlertId"));

                    b.Property<string>("AlertType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR(50)");

                    b.Property<int>("MachineId")
                        .HasColumnType("int");

                    b.Property<double?>("PredictionRisk")
                        .HasColumnType("FLOAT");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(MAX)");

                    b.Property<string>("ResolvedBy")
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR(100)");

                    b.Property<DateTime?>("ResolvedTimestamp")
                        .HasColumnType("DATETIME2");

                    b.Property<string>("Status")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("NVARCHAR(20)")
                        .HasDefaultValue("Pending");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("DATETIME2");

                    b.HasKey("AlertId");

                    b.HasIndex("MachineId", "Timestamp")
                        .HasDatabaseName("IX_Alerts_MachineId_Timestamp");

                    b.ToTable("Alerts");
                });

            modelBuilder.Entity("TheDash.Models.Entities.Localization", b =>
                {
                    b.Property<int>("LocalizationId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("LocalizationId"));

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR(200)");

                    b.Property<string>("Language")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("NVARCHAR(10)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("NVARCHAR(MAX)");

                    b.HasKey("LocalizationId");

                    b.HasIndex("Key", "Language")
                        .IsUnique()
                        .HasDatabaseName("IX_Localizations_Key_Language");

                    b.ToTable("Localizations");
                });

            modelBuilder.Entity("TheDash.Models.Entities.Machine", b =>
                {
                    b.Property<int>("MachineId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("MachineId"));

                    b.Property<bool>("IsVirtual")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("BIT")
                        .HasDefaultValue(true);

                    b.Property<string>("MachineName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("NVARCHAR(100)");

                    b.Property<string>("MachineType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR(50)");

                    b.Property<string>("ProductionLine")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("NVARCHAR(50)");

                    b.HasKey("MachineId");

                    b.HasIndex("MachineName")
                        .IsUnique();

                    b.ToTable("Machines");

                    b.HasData(
                        new
                        {
                            MachineId = 1,
                            IsVirtual = true,
                            MachineName = "Assembly Line 1",
                            MachineType = "Assembly",
                            ProductionLine = "Line A"
                        },
                        new
                        {
                            MachineId = 2,
                            IsVirtual = true,
                            MachineName = "Quality Check 1",
                            MachineType = "QC",
                            ProductionLine = "Line A"
                        },
                        new
                        {
                            MachineId = 3,
                            IsVirtual = true,
                            MachineName = "Packaging Unit 1",
                            MachineType = "Packaging",
                            ProductionLine = "Line B"
                        });
                });

            modelBuilder.Entity("TheDash.Models.Entities.MachineData", b =>
                {
                    b.Property<long>("DataId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("DataId"));

                    b.Property<decimal?>("CycleTime")
                        .HasPrecision(18, 2)
                        .HasColumnType("DECIMAL(18, 2)");

                    b.Property<decimal?>("DefectRate")
                        .HasPrecision(5, 2)
                        .HasColumnType("DECIMAL(5, 2)");

                    b.Property<string>("DefectType")
                        .HasMaxLength(200)
                        .HasColumnType("NVARCHAR(200)");

                    b.Property<decimal?>("Downtime")
                        .HasPrecision(18, 2)
                        .HasColumnType("DECIMAL(18, 2)");

                    b.Property<int>("MachineId")
                        .HasColumnType("int");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("NVARCHAR(20)");

                    b.Property<int?>("Throughput")
                        .HasColumnType("INT");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("DATETIME2");

                    b.Property<decimal?>("Uptime")
                        .HasPrecision(18, 2)
                        .HasColumnType("DECIMAL(18, 2)");

                    b.Property<decimal?>("YieldRate")
                        .HasPrecision(5, 2)
                        .HasColumnType("DECIMAL(5, 2)");

                    b.HasKey("DataId");

                    b.HasIndex("MachineId", "Timestamp")
                        .HasDatabaseName("IX_MachineData_MachineId_Timestamp");

                    b.ToTable("MachineData");

                    b.HasData(
                        new
                        {
                            DataId = 1L,
                            CycleTime = 2.5m,
                            DefectRate = 2.1m,
                            DefectType = "Minor scratches",
                            Downtime = 4.5m,
                            MachineId = 1,
                            Status = "Running",
                            Throughput = 150,
                            Timestamp = new DateTime(2025, 1, 8, 11, 30, 0, 0, DateTimeKind.Unspecified),
                            Uptime = 95.5m,
                            YieldRate = 95.5m
                        },
                        new
                        {
                            DataId = 2L,
                            CycleTime = 1.8m,
                            DefectRate = 0.5m,
                            Downtime = 11.8m,
                            MachineId = 2,
                            Status = "Idle",
                            Throughput = 80,
                            Timestamp = new DateTime(2025, 1, 8, 11, 45, 0, 0, DateTimeKind.Unspecified),
                            Uptime = 88.2m,
                            YieldRate = 98.2m
                        },
                        new
                        {
                            DataId = 3L,
                            CycleTime = 3.2m,
                            DefectRate = 1.2m,
                            DefectType = "Packaging misalignment",
                            Downtime = 7.9m,
                            MachineId = 3,
                            Status = "Running",
                            Throughput = 120,
                            Timestamp = new DateTime(2025, 1, 8, 11, 55, 0, 0, DateTimeKind.Unspecified),
                            Uptime = 92.1m,
                            YieldRate = 96.8m
                        });
                });

            modelBuilder.Entity("TheDash.Models.Entities.Alert", b =>
                {
                    b.HasOne("TheDash.Models.Entities.Machine", "Machine")
                        .WithMany("Alerts")
                        .HasForeignKey("MachineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Machine");
                });

            modelBuilder.Entity("TheDash.Models.Entities.MachineData", b =>
                {
                    b.HasOne("TheDash.Models.Entities.Machine", "Machine")
                        .WithMany("MachineData")
                        .HasForeignKey("MachineId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Machine");
                });

            modelBuilder.Entity("TheDash.Models.Entities.Machine", b =>
                {
                    b.Navigation("Alerts");

                    b.Navigation("MachineData");
                });
#pragma warning restore 612, 618
        }
    }
}
