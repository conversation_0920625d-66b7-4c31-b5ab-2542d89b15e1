/* Dashboard Styles */

/* KPI Cards */
.kpi-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.kpi-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.kpi-icon.bg-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.kpi-icon.bg-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.kpi-icon.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

/* Machine Cards */
.machine-card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.15s ease-in-out;
    height: 100%;
}

.machine-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.machine-card .card-body {
    padding: 1rem;
}

.machine-card .card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #495057;
}

/* Chart Containers */
.card canvas {
    max-height: 400px;
}

/* Filter Section */
#dashboardFilters .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

#dashboardFilters .form-control,
#dashboardFilters .form-select {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#dashboardFilters .form-control:focus,
#dashboardFilters .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Status Badges */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
}

/* Header Section */
.container-fluid h1 {
    color: #495057;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .kpi-card .d-flex {
        flex-direction: column;
        text-align: center;
    }
    
    .kpi-icon {
        margin: 0 auto 1rem auto;
    }
    
    .machine-card .d-flex {
        flex-direction: column;
    }
    
    .machine-card .badge {
        align-self: flex-start;
        margin-top: 0.5rem;
    }
}

@media (max-width: 576px) {
    #dashboardFilters .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .kpi-card {
        margin-bottom: 1rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Tooltips Enhancement */
.chartjs-tooltip {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 0.375rem;
    color: white;
    font-size: 0.875rem;
    padding: 0.5rem;
}

/* Error States */
.error-message {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 0.75rem 1.25rem;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
}

/* Auto-refresh Status */
#refreshStatus {
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

#refreshStatus:hover {
    transform: scale(1.05);
}

/* Card Headers */
.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 500;
}

.card-header h5 {
    color: #495057;
    margin-bottom: 0;
}

/* Smooth Transitions */
* {
    transition: all 0.15s ease-in-out;
}
