# 2. Technology Stack Validation

| Category | Technology | Version | Purpose |
|---|---|---|---|
| Backend | ASP.NET Core MVC | 8.0+ | High-performance backend service, handling API requests and business logic. |
| Database | SQL Server TSQL | 2019+ | Enterprise-grade relational database for storing structured data. |
| Frontend | HTML/CSS/JS | Latest | Standard frontend stack for implementing the user interface and interactions. |
| **[Added]** AI/ML | Python + Scikit-learn | 3.10+ | To implement the predictive maintenance model. |
| Deployment | Local Execution | N/A | Simple local deployment, without dependencies on Docker or cloud services. |

