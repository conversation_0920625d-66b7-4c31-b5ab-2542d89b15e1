using TheDash.Models.Entities;

namespace TheDash.Models
{
    /// <summary>
    /// View model for dashboard page
    /// Contains all data needed for dashboard display
    /// </summary>
    public class DashboardViewModel
    {
        /// <summary>
        /// Total throughput across all machines
        /// </summary>
        public int? TotalThroughput { get; set; }

        /// <summary>
        /// Average yield rate percentage across all machines
        /// </summary>
        public decimal? AverageYieldRate { get; set; }

        /// <summary>
        /// Total downtime in hours across all machines
        /// </summary>
        public decimal? TotalDowntime { get; set; }

        /// <summary>
        /// List of machines with their current status and metrics
        /// </summary>
        public List<DashboardMachine> Machines { get; set; } = new List<DashboardMachine>();

        /// <summary>
        /// Available production lines for filtering
        /// </summary>
        public List<string> ProductionLines { get; set; } = new List<string>();

        /// <summary>
        /// Available machine types for filtering
        /// </summary>
        public List<string> MachineTypes { get; set; } = new List<string>();
    }

    /// <summary>
    /// Comprehensive dashboard data for API responses
    /// </summary>
    public class DashboardOverviewData
    {
        /// <summary>
        /// Total throughput across all machines
        /// </summary>
        public int TotalThroughput { get; set; }

        /// <summary>
        /// Average yield rate percentage across all machines
        /// </summary>
        public decimal AverageYieldRate { get; set; }

        /// <summary>
        /// Total downtime in hours across all machines
        /// </summary>
        public decimal TotalDowntime { get; set; }

        /// <summary>
        /// List of machines with their current status and metrics
        /// </summary>
        public List<DashboardMachine> Machines { get; set; } = new List<DashboardMachine>();

        /// <summary>
        /// Available production lines for filtering
        /// </summary>
        public List<string> ProductionLines { get; set; } = new List<string>();

        /// <summary>
        /// Available machine types for filtering
        /// </summary>
        public List<string> MachineTypes { get; set; } = new List<string>();

        /// <summary>
        /// Trend data for charts
        /// </summary>
        public TrendData? TrendData { get; set; }

        /// <summary>
        /// Machine status counts for status chart
        /// </summary>
        public StatusCounts? StatusCounts { get; set; }
    }

    /// <summary>
    /// Machine data optimized for dashboard display
    /// </summary>
    public class DashboardMachine
    {
        /// <summary>
        /// Machine identifier
        /// </summary>
        public int MachineId { get; set; }

        /// <summary>
        /// Machine name
        /// </summary>
        public string MachineName { get; set; } = string.Empty;

        /// <summary>
        /// Machine type
        /// </summary>
        public string MachineType { get; set; } = string.Empty;

        /// <summary>
        /// Production line name
        /// </summary>
        public string ProductionLine { get; set; } = string.Empty;

        /// <summary>
        /// Current machine status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Current throughput
        /// </summary>
        public int? Throughput { get; set; }

        /// <summary>
        /// Current yield rate percentage
        /// </summary>
        public decimal? YieldRate { get; set; }

        /// <summary>
        /// Current downtime in hours
        /// </summary>
        public decimal? Downtime { get; set; }

        /// <summary>
        /// Last update timestamp
        /// </summary>
        public DateTime? LastUpdated { get; set; }
    }

    /// <summary>
    /// Trend data for dashboard charts
    /// </summary>
    public class TrendData
    {
        /// <summary>
        /// Time labels for chart x-axis
        /// </summary>
        public List<string> Labels { get; set; } = new List<string>();

        /// <summary>
        /// Throughput values over time
        /// </summary>
        public List<int> Throughput { get; set; } = new List<int>();

        /// <summary>
        /// Yield rate values over time
        /// </summary>
        public List<decimal> YieldRate { get; set; } = new List<decimal>();
    }

    /// <summary>
    /// Machine status counts for status chart
    /// </summary>
    public class StatusCounts
    {
        /// <summary>
        /// Number of running machines
        /// </summary>
        public int Running { get; set; }

        /// <summary>
        /// Number of down machines
        /// </summary>
        public int Down { get; set; }

        /// <summary>
        /// Number of idle machines
        /// </summary>
        public int Idle { get; set; }

        /// <summary>
        /// Number of machines in maintenance
        /// </summary>
        public int Maintenance { get; set; }
    }
}
