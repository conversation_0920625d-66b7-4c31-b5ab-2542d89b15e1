# 5. Deployment Plan

This deployment plan is for local execution.

1.  **Prerequisites:** Install .NET SDK (8.0+), SQL Server, Git, Visual Studio, Python (3.10+), and Scikit-learn.
2.  **Get Code:** Use `git clone` to get the project code.
3.  **Set Up Database:** Create the database and tables, and update the connection string in `appsettings.json`.
4.  **Run Application:** Press F5 in Visual Studio or use `dotnet run` to start the backend application.
5.  **Run AI/ML Service:** Run the independent Python service, which listens for requests from the ASP.NET Core application.
6.  **Access Application:** Access the application via a web browser at `https://localhost:7001/`.

---

### Mermaid Diagrams Plaintext

Below is the plaintext code for the updated system component and deployment diagrams, which now include the new AI/ML service component.

```mermaid
graph TD
    A[Browser/Frontend] -->|HTTP/S Request| B(ASP.NET Core Application)
    B -->|SQL Query| C(SQL Server Database)
    B -->|Data Processing| D(Background Service)
    D -->|Data Generation/Alert Trigger| C
    B --Real-time Data--> E(AI/ML Prediction Service)
    E --Prediction Result--> C
    C -->|Data Return| B
    B -->|HTML/JSON Response| A
```

```mermaid
graph TD
    subgraph Local Machine
        A[Browser] --> B(ASP.NET Core Application)
        C[Background Service] --> B
        D[SQL Server] --> B
        E[AI/ML Prediction Service] --> B
    end
    B --> D
    B -- Local Network --> A
```