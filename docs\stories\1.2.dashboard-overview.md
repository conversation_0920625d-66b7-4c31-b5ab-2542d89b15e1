# Story 1.2: Real-time Dashboard Overview

## Status
Approved

## Story
**As a** production manager,
**I want** to see a real-time dashboard upon login so I can get a quick overview of all production lines' health and key performance indicators,
**so that** I can quickly identify production anomalies or bottlenecks and make informed decisions.

## Acceptance Criteria
1. The dashboard must be displayed on the main page (/dashboard) and automatically refresh data every few seconds.
2. The dashboard must use visual components like cards, gauges, and line charts to clearly display core KPIs such as total throughput, average yield rate, and total downtime.
3. All charts must support tooltips on hover to show detailed data points.
4. Users must be able to navigate to a machine's detailed page by clicking on it in the overview, where they can see machine-specific data.
5. The dashboard must include a filter component that allows users to filter data by date range, production line, and machine type.

## Tasks / Subtasks
- [ ] Task 1: Create Dashboard Controller and Views (AC: 1, 2)
  - [ ] Create DashboardController.cs in Controllers folder
  - [ ] Create Index action method to serve dashboard page
  - [ ] Create Views/Dashboard/Index.cshtml with layout for KPI cards and charts
  - [ ] Implement auto-refresh functionality with JavaScript
- [ ] Task 2: Implement KPI Data Service (AC: 2)
  - [ ] Create DashboardService.cs for business logic
  - [ ] Implement methods to calculate total throughput, average yield rate, total downtime
  - [ ] Create API endpoint /api/dashboard/overview for real-time data
  - [ ] Integrate with existing Machines and MachineData tables
- [ ] Task 3: Create Visual Components (AC: 2, 3)
  - [ ] Implement KPI cards with visual indicators
  - [ ] Add Chart.js or similar library for line charts and gauges
  - [ ] Implement tooltip functionality for detailed data points
  - [ ] Style components for clear data visualization
- [ ] Task 4: Implement Machine Navigation (AC: 4)
  - [ ] Create clickable machine overview components
  - [ ] Implement navigation to machine detail pages
  - [ ] Create machine detail view structure
- [ ] Task 5: Add Filter Component (AC: 5)
  - [ ] Create filter UI for date range, production line, machine type
  - [ ] Implement filter logic in DashboardService
  - [ ] Update API to support filter parameters
  - [ ] Integrate filters with real-time refresh
- [ ] Task 6: Unit Testing (All ACs)
  - [ ] Create unit tests for DashboardController
  - [ ] Create unit tests for DashboardService
  - [ ] Test API endpoints and data calculations
  - [ ] Test filter functionality

## Dev Notes

### Previous Story Insights
From Story 1.1 (Emulator Page):
- Entity Framework integration is already set up with ApplicationDbContext
- Machines and MachineData tables are available and properly indexed
- Service layer pattern established with dependency injection
- AJAX patterns and real-time updates are working well
- Standard ASP.NET Core MVC structure is in place

### Data Models
**Machines Table** [Source: architecture/3-database-architecture-design.md]:
- MachineId (INT, PRIMARY KEY)
- MachineName (NVARCHAR(100), UNIQUE)
- MachineType (NVARCHAR(50))
- ProductionLine (NVARCHAR(50))
- IsVirtual (BIT, DEFAULT(1))

**MachineData Table** [Source: architecture/3-database-architecture-design.md]:
- DataId (BIGINT, PRIMARY KEY CLUSTERED)
- MachineId (INT, FOREIGN KEY)
- Timestamp (DATETIME2)
- Status (NVARCHAR(20))
- Uptime, Downtime, CycleTime (DECIMAL(18,2))
- Throughput (INT)
- YieldRate, DefectRate (DECIMAL(5,2))
- DefectType (NVARCHAR(200))
- Optimized with non-clustered index on (MachineId, Timestamp)

### API Specifications
**Dashboard Overview API** [Source: architecture/4-api-specification.md]:
- HTTP Method: GET
- Endpoint: `/api/dashboard/overview`
- Description: Retrieves overall KPI overview data for all production lines
- Request Parameters: startDate, endDate, productionLine (optional)
- Data Format: JSON

### Component Specifications
**Dashboard Layout Requirements**:
- Main dashboard route: `/dashboard`
- Auto-refresh every few seconds
- Visual components: cards, gauges, line charts
- KPI displays: total throughput, average yield rate, total downtime
- Interactive elements: clickable machines, hover tooltips
- Filter component: date range, production line, machine type

### File Locations
Based on standard ASP.NET Core MVC structure:
- Controller: `/Controllers/DashboardController.cs`
- Views: `/Views/Dashboard/Index.cshtml`
- Service: `/Services/DashboardService.cs`
- Models: `/Models/DashboardViewModel.cs`
- JavaScript: `/wwwroot/js/dashboard.js`
- CSS: `/wwwroot/css/dashboard.css`

### Testing Requirements
No specific testing strategy found in architecture docs. Follow standard ASP.NET Core testing patterns:
- Unit tests in separate test project
- Test controller actions and service methods
- Mock database dependencies for testing
- Test API endpoints and data calculations

### Technical Constraints
**Architecture** [Source: architecture/1-architecture-overview.md]:
- Monolith architecture with single ASP.NET Core MVC application
- SQL Server for data storage
- Frontend communicates with backend via API

**Technology Stack** [Source: architecture/2-technology-stack-validation.md]:
- Backend: ASP.NET Core MVC 8.0+
- Database: SQL Server TSQL 2019+
- Frontend: HTML/CSS/JS (Latest)
- Local deployment without Docker/cloud dependencies

### Project Structure Notes
Aligns with existing project structure from Story 1.1:
- Uses Entity Framework with ApplicationDbContext
- Follows service layer pattern with dependency injection
- Standard MVC folder structure maintained
- Static assets in wwwroot organized by type

## Testing

### Test File Location
- Unit tests in separate test project (TheDash.Tests/)
- Follow existing pattern from Story 1.1

### Test Standards
- Use standard ASP.NET Core testing patterns
- Mock database dependencies using Entity Framework InMemory provider
- Test controller actions, service methods, and API endpoints

### Testing Frameworks and Patterns
- xUnit for unit testing framework
- Moq for mocking dependencies
- Entity Framework InMemory for database testing
- Test both positive and negative scenarios

### Specific Testing Requirements
- Test KPI calculations for accuracy
- Test filter functionality with various parameters
- Test real-time data refresh mechanisms
- Test navigation to machine detail pages
- Verify tooltip functionality and data display

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-08 | 1.0 | Initial story creation | Scrum Master Bob |
