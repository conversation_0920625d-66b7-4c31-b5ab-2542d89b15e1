using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using TheDash.Controllers;
using TheDash.Data;
using TheDash.Models;
using TheDash.Models.Entities;
using TheDash.Services;

namespace TheDash.Tests;

public class DashboardControllerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly IDashboardService _dashboardService;
    private readonly DashboardController _controller;
    private readonly string _testDbName;

    public DashboardControllerTests()
    {
        // Use unique database for each test run
        _testDbName = $"TheDashDb_Dashboard_Test_{Guid.NewGuid():N}";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseInMemoryDatabase(databaseName: _testDbName)
            .Options;

        _context = new ApplicationDbContext(options);

        // Seed test data
        SeedTestData();

        _dashboardService = new DashboardService(_context);
        _controller = new DashboardController(_dashboardService);
    }

    private void SeedTestData()
    {
        // Add test machines
        var machines = new List<Machine>
        {
            new Machine { MachineName = "Machine1", MachineType = "TypeA", ProductionLine = "Line1", IsVirtual = true },
            new Machine { MachineName = "Machine2", MachineType = "TypeB", ProductionLine = "Line1", IsVirtual = true },
            new Machine { MachineName = "Machine3", MachineType = "TypeA", ProductionLine = "Line2", IsVirtual = true }
        };

        _context.Machines.AddRange(machines);
        _context.SaveChanges();

        // Add test machine data
        var machineData = new List<MachineData>
        {
            new MachineData 
            { 
                MachineId = machines[0].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Running",
                Throughput = 100,
                YieldRate = 95.5m,
                Downtime = 0.5m
            },
            new MachineData 
            { 
                MachineId = machines[1].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Down",
                Throughput = 0,
                YieldRate = 0,
                Downtime = 2.0m
            },
            new MachineData 
            { 
                MachineId = machines[2].MachineId, 
                Timestamp = DateTime.Now.AddHours(-1),
                Status = "Running",
                Throughput = 150,
                YieldRate = 98.0m,
                Downtime = 0.1m
            }
        };

        _context.MachineData.AddRange(machineData);
        _context.SaveChanges();
    }

    [Fact]
    public async Task Index_ReturnsViewWithDashboardViewModel()
    {
        // Act
        var result = await _controller.Index();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<DashboardViewModel>(viewResult.Model);
        
        Assert.NotNull(model);
        Assert.True(model.TotalThroughput > 0);
        Assert.True(model.AverageYieldRate > 0);
        Assert.NotEmpty(model.Machines);
        Assert.NotEmpty(model.ProductionLines);
        Assert.NotEmpty(model.MachineTypes);
    }

    [Fact]
    public async Task GetOverviewData_ReturnsJsonWithDashboardData()
    {
        // Act
        var result = await _controller.GetOverviewData();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var data = Assert.IsType<DashboardOverviewData>(jsonResult.Value);
        
        Assert.NotNull(data);
        Assert.Equal(250, data.TotalThroughput); // 100 + 0 + 150
        Assert.True(data.AverageYieldRate > 0);
        Assert.Equal(3, data.Machines.Count);
    }

    [Fact]
    public async Task GetOverviewData_WithProductionLineFilter_ReturnsFilteredData()
    {
        // Act
        var result = await _controller.GetOverviewData(productionLine: "Line1");

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var data = Assert.IsType<DashboardOverviewData>(jsonResult.Value);
        
        Assert.NotNull(data);
        Assert.Equal(100, data.TotalThroughput); // Only Machine1 from Line1 is running
        Assert.Equal(2, data.Machines.Count); // Only machines from Line1
    }

    [Fact]
    public async Task GetOverviewData_WithDateRange_ReturnsFilteredData()
    {
        // Arrange
        var startDate = DateTime.Now.AddDays(-1);
        var endDate = DateTime.Now;

        // Act
        var result = await _controller.GetOverviewData(startDate, endDate);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var data = Assert.IsType<DashboardOverviewData>(jsonResult.Value);
        
        Assert.NotNull(data);
        Assert.True(data.TotalThroughput >= 0);
    }

    [Fact]
    public async Task Index_HandlesServiceException_ReturnsErrorView()
    {
        // Arrange - Create controller with null service to force exception
        var controllerWithNullService = new DashboardController(null!);

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => controllerWithNullService.Index());
    }

    public void Dispose()
    {
        _context.Dispose();
    }
}
