using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using TheDash.Controllers;
using TheDash.Data;
using TheDash.Models;
using TheDash.Models.Entities;
using TheDash.Services;

namespace TheDash.Tests;

public class EmulatorControllerTests : IDisposable
{
    private readonly ApplicationDbContext _context;
    private readonly IEmulatorService _emulatorService;
    private readonly EmulatorController _controller;
    private readonly string _testDbName;

    public EmulatorControllerTests()
    {
        // Use unique database for each test run
        _testDbName = $"TheDashDb_Test_{Guid.NewGuid():N}";
        var connectionString = $"Server=.\\SQLEXPRESS;Database={_testDbName};Trusted_Connection=true;MultipleActiveResultSets=true;TrustServerCertificate=true";
        var options = new DbContextOptionsBuilder<ApplicationDbContext>()
            .UseSqlServer(connectionString)
            .Options;

        _context = new ApplicationDbContext(options);

        // Ensure database is created
        _context.Database.EnsureCreated();

        _emulatorService = new EmulatorService(_context);
        _controller = new EmulatorController(_emulatorService);

        // Seed test data
        SeedTestData();
    }

    private void SeedTestData()
    {
        // Clear any existing data first
        _context.MachineData.RemoveRange(_context.MachineData);
        _context.Machines.RemoveRange(_context.Machines);
        _context.SaveChanges();

        // Use unique names with timestamp to avoid conflicts
        var timestamp = DateTime.Now.Ticks.ToString()[^8..];

        var machines = new List<Machine>
        {
            new()
            {
                MachineName = $"Assembly Line {timestamp}",
                MachineType = "Assembly",
                ProductionLine = "Line A",
                IsVirtual = true
            },
            new()
            {
                MachineName = $"Quality Check {timestamp}",
                MachineType = "QC",
                ProductionLine = "Line A",
                IsVirtual = true
            }
        };

        _context.Machines.AddRange(machines);
        _context.SaveChanges(); // Save machines first to get auto-generated IDs

        // Now create machine data using the generated machine IDs
        var machineData = new List<MachineData>
        {
            new()
            {
                MachineId = machines[0].MachineId, // Use auto-generated ID
                Timestamp = DateTime.UtcNow.AddMinutes(-5),
                Status = "Running",
                Throughput = 150,
                YieldRate = 95.5m,
                CycleTime = 2.5m
            },
            new()
            {
                MachineId = machines[1].MachineId, // Use auto-generated ID
                Timestamp = DateTime.UtcNow.AddMinutes(-2),
                Status = "Idle",
                Throughput = 80,
                YieldRate = 98.2m,
                CycleTime = 1.8m
            }
        };

        _context.MachineData.AddRange(machineData);
        _context.SaveChanges();
    }

    public void Dispose()
    {
        // Clean up test database
        _context.Database.EnsureDeleted();
        _context.Dispose();
    }

    [Fact]
    public async Task Index_ReturnsViewResult_WithEmulatorViewModel()
    {
        // Act
        var result = await _controller.Index();

        // Assert
        var viewResult = Assert.IsType<ViewResult>(result);
        var model = Assert.IsType<EmulatorViewModel>(viewResult.Model);
        Assert.NotNull(model.Machines);
        Assert.Equal(2, model.Machines.Count);
    }

    [Fact]
    public async Task GetMachines_ReturnsJsonResult_WithMachinesList()
    {
        // Act
        var result = await _controller.GetMachines();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var response = jsonResult.Value as ApiResponse<List<VirtualMachine>>;
        Assert.NotNull(response);
        Assert.True(response.Success);
        Assert.Equal(2, response.Data.Count);
    }

    [Fact]
    public async Task CreateMachine_ValidData_ReturnsSuccessResponse()
    {
        // Arrange
        var request = new CreateMachineRequest
        {
            MachineName = "Test Machine",
            MachineType = "Test Type",
            ProductionLine = "Test Line"
        };

        // Act
        var result = await _controller.CreateMachine(request);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);

        // Debug: Check what we actually got
        Assert.NotNull(jsonResult.Value);

        // Check if it's an error response first
        if (jsonResult.Value is ApiResponse<object> errorResponse)
        {
            Assert.Fail($"Expected success but got error: {errorResponse.Message}");
        }

        var response = jsonResult.Value as ApiResponse<VirtualMachine>;
        Assert.NotNull(response);
        Assert.True(response.Success);
        Assert.NotNull(response.Data);
        Assert.Equal("Test Machine", response.Data.MachineName);
    }

    [Fact]
    public async Task CreateMachine_DuplicateName_ReturnsErrorResponse()
    {
        // Arrange - Get the first machine name from seed data
        var machines = await _emulatorService.GetVirtualMachinesAsync();
        var existingMachineName = machines.First().MachineName;

        var request = new CreateMachineRequest
        {
            MachineName = existingMachineName, // Use existing machine name
            MachineType = "Test Type",
            ProductionLine = "Test Line"
        };

        // Act
        var result = await _controller.CreateMachine(request);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var response = jsonResult.Value as ApiResponse<object>;
        Assert.NotNull(response);
        Assert.False(response.Success);
        Assert.Contains("already exists", response.Message);
    }

    [Fact]
    public async Task DeleteMachine_ValidId_ReturnsSuccessResponse()
    {
        // Arrange - Get the first machine ID from seed data
        var machines = await _emulatorService.GetVirtualMachinesAsync();
        var firstMachine = machines.First();

        // Act
        var result = await _controller.DeleteMachine(firstMachine.MachineId);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var response = jsonResult.Value as ApiResponse<object>;
        Assert.NotNull(response);
        Assert.True(response.Success);
    }

    [Fact]
    public async Task AddMachineData_ValidData_ReturnsSuccessResponse()
    {
        // Arrange - Get the first machine ID from seed data
        var machines = await _emulatorService.GetVirtualMachinesAsync();
        var firstMachine = machines.First();

        var dataUpdate = new MachineDataUpdate
        {
            MachineId = firstMachine.MachineId,
            Status = "Running",
            Throughput = 200,
            YieldRate = 97.5m,
            CycleTime = 2.0m
        };

        // Act
        var result = await _controller.AddMachineData(firstMachine.MachineId, dataUpdate);

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var response = jsonResult.Value as ApiResponse<VirtualMachine>;
        Assert.NotNull(response);
        Assert.True(response.Success);
        Assert.Equal(200, response.Data!.Throughput);
        Assert.Equal(97.5m, response.Data.YieldRate);
    }

    [Fact]
    public async Task GetDashboardData_ReturnsFormattedData()
    {
        // Act
        var result = await _controller.GetDashboardData();

        // Assert
        var jsonResult = Assert.IsType<JsonResult>(result);
        var response = jsonResult.Value as ApiResponse<object>;
        Assert.NotNull(response);
        Assert.True(response.Success);
    }
}