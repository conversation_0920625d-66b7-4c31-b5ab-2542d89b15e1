@model TheDash.Models.DashboardViewModel
@{
    ViewData["Title"] = "Production Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/css/dashboard.css" asp-append-version="true" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.css" />
}

<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="mb-1">Production Dashboard</h1>
                    <p class="text-muted mb-0">Real-time overview of production lines and key performance indicators</p>
                </div>
                <div class="text-end">
                    <small class="text-muted">Last updated: <span id="lastUpdated">--</span></small>
                    <br>
                    <span class="badge bg-success" id="refreshStatus">Auto-refresh: ON</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form id="dashboardFilters" class="row g-3">
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate" name="startDate">
                        </div>
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate" name="endDate">
                        </div>
                        <div class="col-md-3">
                            <label for="productionLine" class="form-label">Production Line</label>
                            <select class="form-select" id="productionLine" name="productionLine">
                                <option value="">All Lines</option>
                                @if (Model?.ProductionLines != null)
                                {
                                    @foreach (var line in Model.ProductionLines)
                                    {
                                        <option value="@line">@line</option>
                                    }
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="machineType" class="form-label">Machine Type</label>
                            <select class="form-select" id="machineType" name="machineType">
                                <option value="">All Types</option>
                                @if (Model?.MachineTypes != null)
                                {
                                    @foreach (var type in Model.MachineTypes)
                                    {
                                        <option value="@type">@type</option>
                                    }
                                }
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards Section -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="kpi-icon bg-primary">
                            <i class="fas fa-tachometer-alt"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="card-subtitle mb-1 text-muted">Total Throughput</h6>
                            <h3 class="card-title mb-0" id="totalThroughput">@(Model?.TotalThroughput?.ToString("N0") ?? "0")</h3>
                            <small class="text-muted">units/hour</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="kpi-icon bg-success">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="card-subtitle mb-1 text-muted">Average Yield Rate</h6>
                            <h3 class="card-title mb-0" id="averageYieldRate">@(Model?.AverageYieldRate?.ToString("F1") ?? "0.0")%</h3>
                            <small class="text-muted">quality rate</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card kpi-card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="kpi-icon bg-warning">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="card-subtitle mb-1 text-muted">Total Downtime</h6>
                            <h3 class="card-title mb-0" id="totalDowntime">@(Model?.TotalDowntime?.ToString("F1") ?? "0.0")</h3>
                            <small class="text-muted">hours</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Production Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="productionChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Machine Status</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" width="200" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Machine Overview Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Machine Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="machineOverview">
                        @if (Model?.Machines != null)
                        {
                            @foreach (var machine in Model.Machines)
                            {
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card machine-card" data-machine-id="@machine.MachineId" style="cursor: pointer;">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title">@machine.MachineName</h6>
                                                    <p class="card-text text-muted mb-1">@machine.MachineType</p>
                                                    <small class="text-muted">@machine.ProductionLine</small>
                                                </div>
                                                <span class="badge bg-@(machine.Status == "Running" ? "success" : machine.Status == "Down" ? "danger" : "warning")">
                                                    @machine.Status
                                                </span>
                                            </div>
                                            <div class="mt-2">
                                                <small class="text-muted">
                                                    Throughput: <strong>@(machine.Throughput?.ToString("N0") ?? "0")</strong> |
                                                    Yield: <strong>@(machine.YieldRate?.ToString("F1") ?? "0.0")%</strong>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>
    <script src="~/js/dashboard.js" asp-append-version="true"></script>
}
