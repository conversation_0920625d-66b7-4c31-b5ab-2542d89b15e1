# Comprehensive Production Monitoring and Analytics Dashboard Product Requirements Document (PRD)

**Version:** 2.0
**Created On:** August 5, 2025
**Product Manager:** John

---

## 1. Project Overview

**Vision:** To create a real-time, centralized dashboard that provides a complete overview of PCB production line performance, enabling data-driven decisions to enhance overall operational efficiency, and utilizing AI/ML to achieve predictive maintenance.

**Goals:**
* Improve operational efficiency.
* Enhance decision-making capabilities.
* Enable predictive maintenance.
* Facilitate cross-cultural collaboration.
* **[Added]** Utilize AI/ML technology to predict equipment failures.

**Key Features (Prioritized):**
* **Must Have:** Real-time data collection (virtual data), interactive dashboard (real-time overview, trend charts, drill-down, filters), predictive maintenance alerts, multi-language support (UI and language memory).
* **Should Have:** Customizable reports (generate, export, defect analysis), alert management, localization for reports and notifications.
* **Version 2.0 (AI/ML):** Predictive maintenance based on an AI model.

---

## 2. Functional Requirements

### 2.1 Real-Time Data Collection (using virtual data) - Must Have

**User Stories:**
- As a developer/tester, I want to access a special /emulator page so I can simulate production data for development and testing without needing real hardware.
- As a developer/tester, I want to view, add, edit, and delete virtual machine data on the /emulator page so I can flexibly simulate various production scenarios (e.g., running, downtime, anomalies).
- As a developer/tester, I want to control the state of a virtual machine (e.g., running, down, idle) so I can verify how the dashboard displays data in different states.
- As a developer/tester, I want to adjust a virtual machine's KPI metrics (e.g., throughput, yield rate, cycle time) so I can test the logic for alerts and reports.

**Acceptance Criteria:**
- The system must provide a page at the /emulator route for development and testing only, which is not accessible via any navigation menu.
- This page must offer a CRUD (Create, Read, Update, Delete) interface to manage virtual machines and their data.
- The page must provide controls (e.g., dropdowns, buttons) to manually set each virtual machine's status (Running, Down, Idle, Maintenance).
- The page must have editable fields for users to input and update KPI metrics (e.g., throughput, yield rate, cycle time).
- Data changes made on this page must be reflected in real time on the main dashboard.

### 2.2 Interactive Dashboard - Must Have

**User Stories:**
- As a production manager, I want to see a real-time dashboard upon login so I can get a quick overview of all production lines' health and key performance indicators.
- As a production manager, I want to view real-time KPI trends via charts and graphs on the dashboard so I can quickly identify production anomalies or bottlenecks.
- As a production line supervisor, I want to click on a production line or machine to view its detailed performance data and historical trends so I can analyze its operational status in depth.
- As an operator, I want to filter the dashboard data by a date range, production line, or machine type so I can focus on the specific areas I care about.

**Acceptance Criteria:**
- The dashboard must be displayed on the main page (/dashboard) and automatically refresh data every few seconds.
- The dashboard must use visual components like cards, gauges, and line charts to clearly display core KPIs such as total throughput, average yield rate, and total downtime.
- All charts must support tooltips on hover to show detailed data points.
- Users must be able to navigate to a machine's detailed page by clicking on it in the overview, where they can see machine-specific data.
- The dashboard must include a filter component that allows users to filter data by date range, production line, and machine type.

### 2.3 Predictive Maintenance Alerts - Must Have

**User Stories:**
- As a maintenance engineer, I want to receive automatic alert notifications when the system predicts a machine may be about to fail, so I can perform timely preventative maintenance and avoid unplanned downtime.
- As a maintenance engineer, I want alert notifications to include the affected machine, the trigger reason, and relevant data so I can quickly diagnose the problem.

**Acceptance Criteria:**
- The system must have a background service that continuously monitors virtual machine KPIs and triggers an alert when data exceeds a preset threshold or shows an anomalous trend.
- When an alert is triggered, the system must display a notification prominently on the dashboard and send an email to a predefined list of maintenance personnel.
- The alert notification and email must clearly include the alert type, affected machine ID/name, trigger reason, and a snapshot of the relevant data.
- There must be an alerts management page that allows maintenance personnel to view current and historical alerts.

### 2.4 Multi-Language Support - Must Have

**User Stories:**
- As a user, I want to be able to select between Chinese, Malay, or English to browse the entire system so I can operate in my most familiar language.
- As a user, I want my language preference to be remembered by the system so it automatically loads my preferred language on every visit.

**Acceptance Criteria:**
- The system must provide a language switcher in the top-right corner of the page with options for "中文," "Bahasa Melayu," and "English."
- All static UI text (menus, buttons, labels, table headers, etc.) must switch based on the user's selected language.
- The user's language choice must be persisted via a cookie or user configuration so it loads automatically on subsequent sessions.
- Dynamically generated text (e.g., chart titles, alert messages) must also be localized according to the current language setting.

### 2.5 Customizable Reports - Should Have

**User Stories:**
- As a manager, I want to be able to generate custom production reports on demand so I can perform in-depth analysis on the performance of a specific time period or production line.
- As a manager, I want to be able to export reports as PDF or Excel files so I can easily share them with my team or analyze them offline.
- As a quality control officer, I want to generate a detailed defect analysis report so I can better track and resolve product quality issues.

**Acceptance Criteria:**
- The system must provide a dedicated page that allows users to select a report type (e.g., performance overview, machine performance, defect analysis).
- The page must include a filter component that allows users to customize reports by date range, production line, machine ID, and product type (if applicable).
- The system must be able to export generated reports in both PDF and XLSX formats, ensuring the format is correct and the data is complete.
- The generated reports must include charts and tables consistent with the dashboard, and provide clear titles and data summaries.

### 2.6 Alert Management - Should Have

**User Stories:**
- As a maintenance manager, I want to view a history of all alerts and be able to update their status (e.g., handled, unhandled) so I can better manage my maintenance workflow.

**Acceptance Criteria:**
- The system must provide a dedicated page that displays all historical and current alerts.
- The alert list must include the trigger time, machine ID, alert type, trigger reason, and current status.
- Users must be able to change an alert's status, for example, from "Pending" to "In Progress" or "Resolved."
- The system must log the timestamp and user for each status update.

### 2.7 Localization for Reports and Notifications - Should Have

**User Stories:**
- As a user, I want reports and notifications to also be generated in my selected language so I can share information with colleagues without a language barrier.

**Acceptance Criteria:**
- All text within generated PDF and Excel reports (titles, table headers, chart labels) must be localized based on the user's current language setting.
- The system must send email notifications (e.g., alert notifications) in the user's preferred language.

---

### 3.0 Predictive Maintenance via AI/ML - Version 2.0

**User Stories:**
- As a maintenance engineer, I want the system to analyze machine data using an AI model and predict when a device is likely to fail, so I can schedule maintenance proactively and minimize downtime.

**Acceptance Criteria:**
- The system must have a separate AI/ML service for training and running the predictive model.
- This service must be able to receive real-time machine data from the backend and return a failure risk score or a predicted time to failure.
- The alert system must be able to trigger a "High Risk Alert" based on the AI/ML service's predictions, which should be distinct from traditional threshold-based alerts.
- The dashboard must have a dedicated area to display the AI model's prediction results (e.g., a "Risk Score" or "Time to Failure" for the next 24 hours).

---

## 4. Technology Stack

* **Backend:** ASP.NET Core MVC
* **Database:** SQL Server TSQL
* **Frontend:** HTML, CSS, JavaScript
* **Data Integration:** API or data connectors
* **[Added] AI/ML Service:** Python + Scikit-learn