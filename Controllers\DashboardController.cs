using Microsoft.AspNetCore.Mvc;
using TheDash.Models;
using TheDash.Services;

namespace TheDash.Controllers
{
    /// <summary>
    /// Controller for real-time dashboard functionality
    /// Provides production line overview and KPI monitoring
    /// </summary>
    public class DashboardController : Controller
    {
        private readonly IDashboardService _dashboardService;

        public DashboardController(IDashboardService dashboardService)
        {
            _dashboardService = dashboardService ?? throw new ArgumentNullException(nameof(dashboardService));
        }

        // GET: /dashboard
        public async Task<IActionResult> Index()
        {
            try
            {
                // Get dashboard overview data
                var dashboardData = await _dashboardService.GetDashboardOverviewAsync();
                var viewModel = new DashboardViewModel
                {
                    TotalThroughput = dashboardData.TotalThroughput,
                    AverageYieldRate = dashboardData.AverageYieldRate,
                    TotalDowntime = dashboardData.TotalDowntime,
                    Machines = dashboardData.Machines,
                    ProductionLines = dashboardData.ProductionLines,
                    MachineTypes = dashboardData.MachineTypes
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                // Log error and return error view
                // TODO: Add proper logging
                ViewBag.ErrorMessage = "Unable to load dashboard data. Please try again.";
                return View("Error");
            }
        }

        // API: GET /api/dashboard/overview
        [HttpGet]
        [Route("api/dashboard/overview")]
        public async Task<JsonResult> GetOverviewData(DateTime? startDate = null, DateTime? endDate = null, string? productionLine = null)
        {
            try
            {
                var dashboardData = await _dashboardService.GetDashboardOverviewAsync(startDate, endDate, productionLine);
                return Json(dashboardData);
            }
            catch (Exception ex)
            {
                // TODO: Add proper logging
                return Json(new { error = "Unable to retrieve dashboard data" });
            }
        }
    }
}
