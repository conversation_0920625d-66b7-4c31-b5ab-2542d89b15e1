using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace TheDash.Models.Entities
{
    [Table("Machines")]
    public class Machine
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int MachineId { get; set; }

        [Required]
        [StringLength(100)]
        [Column(TypeName = "NVARCHAR(100)")]
        public string MachineName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column(TypeName = "NVARCHAR(50)")]
        public string MachineType { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        [Column(TypeName = "NVARCHAR(50)")]
        public string ProductionLine { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "BIT")]
        public bool IsVirtual { get; set; } = true;

        // Navigation properties
        public virtual ICollection<MachineData> MachineData { get; set; } = new List<MachineData>();
        public virtual ICollection<Alert> Alerts { get; set; } = new List<Alert>();
    }
}
