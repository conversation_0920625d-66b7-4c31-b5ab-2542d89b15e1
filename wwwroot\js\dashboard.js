// Dashboard JavaScript for real-time updates and interactivity
class DashboardManager {
    constructor() {
        this.refreshInterval = null;
        this.refreshRate = 5000; // 5 seconds
        this.charts = {};
        this.isAutoRefreshEnabled = true;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.startAutoRefresh();
        this.updateLastRefreshTime();
    }

    setupEventListeners() {
        // Filter form submission
        document.getElementById('dashboardFilters').addEventListener('change', () => {
            this.refreshDashboard();
        });

        // Machine card click events for navigation
        document.addEventListener('click', (e) => {
            const machineCard = e.target.closest('.machine-card');
            if (machineCard) {
                const machineId = machineCard.dataset.machineId;
                this.navigateToMachineDetail(machineId);
            }
        });

        // Auto-refresh toggle (if needed)
        document.getElementById('refreshStatus')?.addEventListener('click', () => {
            this.toggleAutoRefresh();
        });
    }

    async refreshDashboard() {
        try {
            const filters = this.getFilterValues();
            const response = await fetch(`/api/dashboard/overview?${new URLSearchParams(filters)}`);
            
            if (!response.ok) {
                throw new Error('Failed to fetch dashboard data');
            }

            const data = await response.json();
            this.updateKPIs(data);
            this.updateCharts(data);
            this.updateMachineOverview(data);
            this.updateLastRefreshTime();
            
        } catch (error) {
            console.error('Error refreshing dashboard:', error);
            this.showError('Failed to refresh dashboard data');
        }
    }

    getFilterValues() {
        return {
            startDate: document.getElementById('startDate').value || '',
            endDate: document.getElementById('endDate').value || '',
            productionLine: document.getElementById('productionLine').value || '',
            machineType: document.getElementById('machineType').value || ''
        };
    }

    updateKPIs(data) {
        document.getElementById('totalThroughput').textContent = 
            data.totalThroughput ? data.totalThroughput.toLocaleString() : '0';
        document.getElementById('averageYieldRate').textContent = 
            data.averageYieldRate ? data.averageYieldRate.toFixed(1) + '%' : '0.0%';
        document.getElementById('totalDowntime').textContent = 
            data.totalDowntime ? data.totalDowntime.toFixed(1) : '0.0';
    }

    initializeCharts() {
        // Production Trends Chart
        const productionCtx = document.getElementById('productionChart').getContext('2d');
        this.charts.production = new Chart(productionCtx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Throughput',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Yield Rate (%)',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                }
            }
        });

        // Machine Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        this.charts.status = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Running', 'Down', 'Idle', 'Maintenance'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(220, 53, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(108, 117, 125, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + ' machines';
                            }
                        }
                    }
                }
            }
        });
    }

    updateCharts(data) {
        // Update production trends chart with new data
        if (data.trendData) {
            this.charts.production.data.labels = data.trendData.labels || [];
            this.charts.production.data.datasets[0].data = data.trendData.throughput || [];
            this.charts.production.data.datasets[1].data = data.trendData.yieldRate || [];
            this.charts.production.update();
        }

        // Update status chart with machine status counts
        if (data.statusCounts) {
            this.charts.status.data.datasets[0].data = [
                data.statusCounts.running || 0,
                data.statusCounts.down || 0,
                data.statusCounts.idle || 0,
                data.statusCounts.maintenance || 0
            ];
            this.charts.status.update();
        }
    }

    updateMachineOverview(data) {
        if (!data.machines) return;

        const container = document.getElementById('machineOverview');
        container.innerHTML = '';

        data.machines.forEach(machine => {
            const statusClass = machine.status === 'Running' ? 'success' : 
                               machine.status === 'Down' ? 'danger' : 'warning';
            
            const machineCard = `
                <div class="col-md-6 col-lg-4 mb-3">
                    <div class="card machine-card" data-machine-id="${machine.machineId}" style="cursor: pointer;">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title">${machine.machineName}</h6>
                                    <p class="card-text text-muted mb-1">${machine.machineType}</p>
                                    <small class="text-muted">${machine.productionLine}</small>
                                </div>
                                <span class="badge bg-${statusClass}">${machine.status}</span>
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">
                                    Throughput: <strong>${(machine.throughput || 0).toLocaleString()}</strong> |
                                    Yield: <strong>${(machine.yieldRate || 0).toFixed(1)}%</strong>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            container.innerHTML += machineCard;
        });
    }

    navigateToMachineDetail(machineId) {
        // Navigate to machine detail page
        window.location.href = `/machine/detail/${machineId}`;
    }

    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        if (this.isAutoRefreshEnabled) {
            this.refreshInterval = setInterval(() => {
                this.refreshDashboard();
            }, this.refreshRate);
        }
    }

    toggleAutoRefresh() {
        this.isAutoRefreshEnabled = !this.isAutoRefreshEnabled;
        const statusElement = document.getElementById('refreshStatus');
        
        if (this.isAutoRefreshEnabled) {
            statusElement.textContent = 'Auto-refresh: ON';
            statusElement.className = 'badge bg-success';
            this.startAutoRefresh();
        } else {
            statusElement.textContent = 'Auto-refresh: OFF';
            statusElement.className = 'badge bg-secondary';
            if (this.refreshInterval) {
                clearInterval(this.refreshInterval);
                this.refreshInterval = null;
            }
        }
    }

    updateLastRefreshTime() {
        const now = new Date();
        document.getElementById('lastUpdated').textContent = now.toLocaleTimeString();
    }

    showError(message) {
        // Simple error display - could be enhanced with toast notifications
        console.error(message);
        // TODO: Implement proper error notification system
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new DashboardManager();
});
