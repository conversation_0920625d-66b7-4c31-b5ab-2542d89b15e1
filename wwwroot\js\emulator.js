// Virtual Machine Emulator JavaScript
class EmulatorManager {
    constructor() {
        this.machines = [];
        this.init();
    }

    init() {
        this.loadMachines();
        this.bindEvents();
    }

    bindEvents() {
        // Add machine form
        document.getElementById('saveMachineBtn').addEventListener('click', () => this.saveMachine());
        document.getElementById('addMachineForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveMachine();
        });

        // Update machine form
        document.getElementById('updateMachineBtn').addEventListener('click', () => this.updateMachine());
        document.getElementById('editMachineForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateMachine();
        });

        // KPI update form
        document.getElementById('kpiUpdateForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.updateMachineData();
        });

        // Machine selection change
        document.getElementById('machineSelect').addEventListener('change', (e) => {
            this.loadMachineData(e.target.value);
        });
    }

    async loadMachines() {
        try {
            const response = await fetch('/api/emulator/machines');
            const result = await response.json();
            
            if (result.success) {
                this.machines = result.data;
                this.renderMachinesTable();
                this.populateMachineSelect();
            } else {
                this.showAlert('Error loading machines: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error loading machines: ' + error.message, 'danger');
        }
    }

    renderMachinesTable() {
        const tbody = document.querySelector('#machinesTable tbody');
        tbody.innerHTML = '';

        this.machines.forEach(machine => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${machine.machineId}</td>
                <td>${machine.machineName}</td>
                <td>${machine.machineType}</td>
                <td>${machine.productionLine}</td>
                <td>
                    <select class="form-select form-select-sm status-select" data-machine-id="${machine.machineId}">
                        <option value="Running" ${machine.status === 'Running' ? 'selected' : ''}>Running</option>
                        <option value="Down" ${machine.status === 'Down' ? 'selected' : ''}>Down</option>
                        <option value="Idle" ${machine.status === 'Idle' ? 'selected' : ''}>Idle</option>
                        <option value="Maintenance" ${machine.status === 'Maintenance' ? 'selected' : ''}>Maintenance</option>
                    </select>
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm kpi-input" 
                           data-machine-id="${machine.machineId}" data-field="throughput" 
                           value="${machine.throughput || ''}" min="0" step="1">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm kpi-input" 
                           data-machine-id="${machine.machineId}" data-field="yieldRate" 
                           value="${machine.yieldRate || ''}" min="0" max="100" step="0.01">
                </td>
                <td>
                    <input type="number" class="form-control form-control-sm kpi-input" 
                           data-machine-id="${machine.machineId}" data-field="cycleTime" 
                           value="${machine.cycleTime || ''}" min="0" step="0.01">
                </td>
                <td>
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="emulator.editMachine(${machine.machineId})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="emulator.deleteMachine(${machine.machineId})">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-success" onclick="emulator.updateMachineDataFromRow(${machine.machineId})">
                        <i class="fas fa-save"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // Bind status change events
        document.querySelectorAll('.status-select').forEach(select => {
            select.addEventListener('change', (e) => {
                const machineId = parseInt(e.target.dataset.machineId);
                this.updateMachineStatus(machineId, e.target.value);
            });
        });

        // Bind KPI input change events for real-time updates
        document.querySelectorAll('.kpi-input').forEach(input => {
            input.addEventListener('blur', (e) => {
                const machineId = parseInt(e.target.dataset.machineId);
                this.updateMachineDataFromRow(machineId);
            });
        });
    }

    populateMachineSelect() {
        const select = document.getElementById('machineSelect');
        select.innerHTML = '<option value="">Choose a machine...</option>';
        
        this.machines.forEach(machine => {
            const option = document.createElement('option');
            option.value = machine.machineId;
            option.textContent = `${machine.machineName} (${machine.machineType})`;
            select.appendChild(option);
        });
    }

    loadMachineData(machineId) {
        if (!machineId) return;
        
        const machine = this.machines.find(m => m.machineId == machineId);
        if (machine) {
            document.getElementById('statusSelect').value = machine.status || '';
            document.getElementById('throughputInput').value = machine.throughput || '';
            document.getElementById('yieldRateInput').value = machine.yieldRate || '';
            document.getElementById('cycleTimeInput').value = machine.cycleTime || '';
        }
    }

    async saveMachine() {
        const form = document.getElementById('addMachineForm');
        const formData = new FormData(form);
        
        const machineData = {
            machineName: document.getElementById('machineNameInput').value,
            machineType: document.getElementById('machineTypeInput').value,
            productionLine: document.getElementById('productionLineInput').value
        };

        try {
            const response = await fetch('/api/emulator/machines', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(machineData)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Machine created successfully!', 'success');
                form.reset();
                bootstrap.Modal.getInstance(document.getElementById('addMachineModal')).hide();
                this.loadMachines();
            } else {
                this.showAlert('Error creating machine: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error creating machine: ' + error.message, 'danger');
        }
    }

    editMachine(machineId) {
        const machine = this.machines.find(m => m.machineId === machineId);
        if (machine) {
            document.getElementById('editMachineId').value = machine.machineId;
            document.getElementById('editMachineNameInput').value = machine.machineName;
            document.getElementById('editMachineTypeInput').value = machine.machineType;
            document.getElementById('editProductionLineInput').value = machine.productionLine;
            
            new bootstrap.Modal(document.getElementById('editMachineModal')).show();
        }
    }

    async updateMachine() {
        const machineId = document.getElementById('editMachineId').value;
        const machineData = {
            machineName: document.getElementById('editMachineNameInput').value,
            machineType: document.getElementById('editMachineTypeInput').value,
            productionLine: document.getElementById('editProductionLineInput').value
        };

        try {
            const response = await fetch(`/api/emulator/machines/${machineId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(machineData)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Machine updated successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editMachineModal')).hide();
                this.loadMachines();
            } else {
                this.showAlert('Error updating machine: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error updating machine: ' + error.message, 'danger');
        }
    }

    async deleteMachine(machineId) {
        if (!confirm('Are you sure you want to delete this machine?')) return;

        try {
            const response = await fetch(`/api/emulator/machines/${machineId}`, {
                method: 'DELETE'
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Machine deleted successfully!', 'success');
                this.loadMachines();
            } else {
                this.showAlert('Error deleting machine: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error deleting machine: ' + error.message, 'danger');
        }
    }

    async updateMachineStatus(machineId, status) {
        const machine = this.machines.find(m => m.machineId === machineId);
        if (!machine) return;

        const dataUpdate = {
            machineId: machineId,
            status: status,
            uptime: machine.uptime,
            downtime: machine.downtime,
            cycleTime: machine.cycleTime,
            throughput: machine.throughput,
            yieldRate: machine.yieldRate,
            defectRate: machine.defectRate,
            defectType: machine.defectType
        };

        await this.sendMachineDataUpdate(machineId, dataUpdate);
    }

    async updateMachineDataFromRow(machineId) {
        const statusSelect = document.querySelector(`.status-select[data-machine-id="${machineId}"]`);
        const throughputInput = document.querySelector(`.kpi-input[data-machine-id="${machineId}"][data-field="throughput"]`);
        const yieldRateInput = document.querySelector(`.kpi-input[data-machine-id="${machineId}"][data-field="yieldRate"]`);
        const cycleTimeInput = document.querySelector(`.kpi-input[data-machine-id="${machineId}"][data-field="cycleTime"]`);

        const dataUpdate = {
            machineId: machineId,
            status: statusSelect.value,
            throughput: throughputInput.value ? parseInt(throughputInput.value) : null,
            yieldRate: yieldRateInput.value ? parseFloat(yieldRateInput.value) : null,
            cycleTime: cycleTimeInput.value ? parseFloat(cycleTimeInput.value) : null
        };

        await this.sendMachineDataUpdate(machineId, dataUpdate);
    }

    async updateMachineData() {
        const machineId = document.getElementById('machineSelect').value;
        if (!machineId) {
            this.showAlert('Please select a machine first', 'warning');
            return;
        }

        const dataUpdate = {
            machineId: parseInt(machineId),
            status: document.getElementById('statusSelect').value,
            throughput: document.getElementById('throughputInput').value ? parseInt(document.getElementById('throughputInput').value) : null,
            yieldRate: document.getElementById('yieldRateInput').value ? parseFloat(document.getElementById('yieldRateInput').value) : null,
            cycleTime: document.getElementById('cycleTimeInput').value ? parseFloat(document.getElementById('cycleTimeInput').value) : null
        };

        await this.sendMachineDataUpdate(machineId, dataUpdate);
    }

    async sendMachineDataUpdate(machineId, dataUpdate) {
        try {
            const response = await fetch(`/api/emulator/machines/${machineId}/data`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(dataUpdate)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Machine data updated successfully!', 'success');
                this.loadMachines(); // Refresh the table
            } else {
                this.showAlert('Error updating machine data: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error updating machine data: ' + error.message, 'danger');
        }
    }

    async testDashboardIntegration() {
        try {
            const response = await fetch('/api/emulator/dashboard-data');
            const result = await response.json();

            if (result.success) {
                this.displayDashboardTestResults(result.data);
                this.showAlert('Dashboard integration test successful!', 'success');
            } else {
                this.showAlert('Dashboard integration test failed: ' + result.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Dashboard integration test error: ' + error.message, 'danger');
        }
    }

    displayDashboardTestResults(data) {
        const container = document.getElementById('dashboardTestResults');

        if (!data || data.length === 0) {
            container.innerHTML = '<p class="text-muted">No machine data available for dashboard.</p>';
            return;
        }

        let html = `
            <h6>Simulated Dashboard Data (${data.length} machines):</h6>
            <div class="row">
        `;

        data.forEach(machine => {
            const statusClass = machine.status.toLowerCase();
            const onlineIndicator = machine.isOnline ?
                '<span class="badge bg-success">Online</span>' :
                '<span class="badge bg-secondary">Offline</span>';

            html += `
                <div class="col-md-4 mb-3">
                    <div class="card border-${this.getStatusColor(machine.status)}">
                        <div class="card-body">
                            <h6 class="card-title">${machine.machineName} ${onlineIndicator}</h6>
                            <p class="card-text">
                                <small class="text-muted">Status: ${machine.status}</small><br>
                                <strong>Throughput:</strong> ${machine.throughput || 'N/A'}<br>
                                <strong>Yield Rate:</strong> ${machine.yieldRate ? machine.yieldRate + '%' : 'N/A'}<br>
                                <strong>Cycle Time:</strong> ${machine.cycleTime || 'N/A'}<br>
                                <small class="text-muted">Updated: ${new Date(machine.lastUpdated).toLocaleTimeString()}</small>
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;
    }

    getStatusColor(status) {
        switch (status.toLowerCase()) {
            case 'running': return 'success';
            case 'down': return 'danger';
            case 'idle': return 'warning';
            case 'maintenance': return 'info';
            default: return 'secondary';
        }
    }

    showAlert(message, type) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at top of container
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Initialize emulator when page loads
let emulator;
document.addEventListener('DOMContentLoaded', function() {
    emulator = new EmulatorManager();
});
